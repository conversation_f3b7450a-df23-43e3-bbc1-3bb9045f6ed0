#!/usr/bin/env python3
"""
测试GPT TTS + 火山引擎ASR集成
"""

import os
from dotenv import load_dotenv
from gpt_tts_api import GPTTextToSpeechAPI
from volcano_api import VolcanoSpeechAPI, convert_to_srt

load_dotenv()

def test_integration():
    # 获取API密钥
    gpt_api_key = os.getenv('GPT_API_KEY', 'sk-kmhk2OUYmq5SskSR2e2f1d9cCb5c4e9dB7A3118116548fDe')
    volcano_app_id = os.getenv('VOLCANO_APP_ID')
    volcano_access_token = os.getenv('VOLCANO_ACCESS_TOKEN')
    
    if not all([gpt_api_key, volcano_app_id, volcano_access_token]):
        print("错误：请配置所有必需的API密钥")
        return
    
    # 初始化API
    gpt_api = GPTTextToSpeechAPI(gpt_api_key)
    volcano_api = VolcanoSpeechAPI(volcano_app_id, volcano_access_token)
    
    # 测试文本
    test_text = "这是一个测试。我们要验证GPT生成的音频，能否通过火山引擎获得精确的字幕时间戳。"
    
    print("="*60)
    print("测试GPT TTS + 火山引擎ASR集成")
    print("="*60)
    
    # 1. 使用GPT生成音频
    print("\n1. 使用GPT TTS生成音频...")
    result = gpt_api.text_to_speech(
        text=test_text,
        voice="alloy",
        model="tts-1",
        response_format="mp3"
    )
    
    if not result:
        print("GPT TTS失败")
        return
    
    print(f"返回结果类型: {type(result)}")
    print(f"返回结果: {result}")
    
    # 提取音频路径
    if isinstance(result, dict):
        audio_path = result.get('audio_path')
        print(f"提取的音频路径: {audio_path}")
    else:
        audio_path = result
        print(f"音频路径: {audio_path}")
    
    if not audio_path or not os.path.exists(audio_path):
        print("音频文件不存在")
        return
    
    print(f"✓ 音频文件大小: {os.path.getsize(audio_path) / 1024:.2f} KB")
    
    # 2. 使用火山引擎ASR生成字幕
    print("\n2. 使用火山引擎ASR分析音频...")
    
    # 如果是MP3，先转换为WAV
    if audio_path.endswith('.mp3'):
        print("转换MP3为WAV格式...")
        from moviepy.editor import AudioFileClip
        import tempfile
        
        audio_clip = AudioFileClip(audio_path)
        wav_path = tempfile.NamedTemporaryFile(suffix='.wav', delete=False).name
        audio_clip.write_audiofile(wav_path, verbose=False, logger=None)
        audio_path_for_asr = wav_path
        print(f"✓ 转换完成: {wav_path}")
    else:
        audio_path_for_asr = audio_path
    
    # 提交ASR任务
    with open(audio_path_for_asr, 'rb') as audio_file:
        submit_result = volcano_api.submit_audio(
            audio_file=audio_file,
            words_per_line=20,
            max_lines=2,
            language="zh-CN",
            use_punc=True,
            caption_type="speech"
        )
    
    if submit_result.get("code") != 0:
        print(f"ASR提交失败: {submit_result.get('message')}")
        return
    
    task_id = submit_result.get("id")
    print(f"✓ ASR任务ID: {task_id}")
    
    # 查询结果
    print("等待ASR处理...")
    result = volcano_api.query_result(task_id, blocking=True)
    
    if result.get("code") != 0:
        print(f"ASR失败: {result.get('message')}")
        return
    
    # 显示结果
    utterances = result.get("utterances", [])
    print(f"\n✓ 获取到 {len(utterances)} 条字幕:")
    
    for i, utt in enumerate(utterances, 1):
        start = utt['start_time'] / 1000
        end = utt['end_time'] / 1000
        text = utt['text']
        print(f"  {i}. [{start:.3f}s - {end:.3f}s]: {text}")
    
    # 保存SRT文件
    srt_content = convert_to_srt(utterances)
    with open("test_output.srt", 'w', encoding='utf-8') as f:
        f.write(srt_content)
    print(f"\n✓ 字幕已保存: test_output.srt")
    
    # 清理临时文件
    if 'wav_path' in locals():
        os.unlink(wav_path)
    if isinstance(result, dict) and 'audio_path' in result:
        if os.path.exists(result['audio_path']):
            os.unlink(result['audio_path'])

if __name__ == "__main__":
    test_integration()
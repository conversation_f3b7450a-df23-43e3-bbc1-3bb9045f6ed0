#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QPushButton, QLabel, QStackedWidget,
                               QTextEdit, QComboBox, QCheckBox, QFileDialog, 
                               QMessageBox, QProgressBar, QScrollArea, QGridLayout,
                               QListWidget, QListWidgetItem, QSplitter, QTextBrowser,
                               QTabWidget, QGroupBox, QLineEdit, QSpinBox)
from PySide6.QtCore import Qt, QThread, Signal, Slot, QTimer
from PySide6.QtGui import QPixmap
from datetime import datetime
from dotenv import load_dotenv
import asyncio

# 导入项目模块
from volcano_api import VolcanoSpeechAPI, VOLCANO_VOICE_OPTIONS
from database import Database
from file_manager import FileManager
from workflow_audio import run_audio_workflow

load_dotenv()

class AsyncWorker(QThread):
    """异步任务工作线程"""
    progress_update = Signal(str, str, int, str)
    task_completed = Signal(bool, str)
    
    def __init__(self, task_func, *args, **kwargs):
        super().__init__()
        self.task_func = task_func
        self.args = args
        self.kwargs = kwargs
        
    def run(self):
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(
                self.task_func(*self.args, **self.kwargs)
            )
            self.task_completed.emit(result, "任务完成")
        except Exception as e:
            self.task_completed.emit(False, str(e))
        finally:
            if loop:
                loop.close()

class ProjectManagementWidget(QWidget):
    """项目管理界面 - 列表和详情组合"""
    def __init__(self, db, file_manager):
        super().__init__()
        self.db = db
        self.file_manager = file_manager
        self.current_project_id = None
        self.init_ui()
        
    def init_ui(self):
        # 使用分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：项目列表
        left_widget = QWidget()
        left_layout = QVBoxLayout()
        
        # 列表标题和刷新按钮
        header_layout = QHBoxLayout()
        header_layout.addWidget(QLabel("项目列表"))
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_list)
        header_layout.addWidget(refresh_btn)
        
        left_layout.addLayout(header_layout)
        
        # 项目列表
        self.project_list = QListWidget()
        self.project_list.itemClicked.connect(self.on_item_clicked)
        left_layout.addWidget(self.project_list)
        
        left_widget.setLayout(left_layout)
        
        # 右侧：项目详情
        self.detail_widget = QWidget()
        self.detail_layout = QVBoxLayout()
        
        # 初始提示
        self.empty_label = QLabel("请从左侧选择一个项目")
        self.empty_label.setAlignment(Qt.AlignCenter)
        self.detail_layout.addWidget(self.empty_label)
        
        self.detail_widget.setLayout(self.detail_layout)
        
        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(self.detail_widget)
        splitter.setSizes([300, 700])
        
        # 主布局
        main_layout = QHBoxLayout()
        main_layout.addWidget(splitter)
        self.setLayout(main_layout)
        
        # 初始加载
        self.refresh_list()
        
    def refresh_list(self):
        """刷新项目列表"""
        self.project_list.clear()
        projects = self.db.get_all_projects()
        
        for project in projects:
            # 创建列表项
            status_map = {
                'pending': '⏳ 待处理',
                'processing': '🔄 处理中',
                'completed': '✅ 已完成',
                'failed': '❌ 失败'
            }
            
            item_text = f"{project['name']} - {status_map.get(project['status'], project['status'])}"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, project)
            self.project_list.addItem(item)
            
    def on_item_clicked(self, item):
        """点击项目项"""
        project_data = item.data(Qt.UserRole)
        self.load_project_detail(project_data['id'])
        
    def load_project_detail(self, project_id):
        """加载项目详情"""
        self.current_project_id = project_id
        
        # 清空详情区域
        while self.detail_layout.count():
            child = self.detail_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
                
        # 获取详细信息
        details = self.db.get_project_details(project_id)
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        
        # 内容容器
        content = QWidget()
        content_layout = QVBoxLayout()
        
        # 项目信息
        info_group = QGroupBox("项目信息")
        info_layout = QVBoxLayout()
        info_layout.addWidget(QLabel(f"名称: {details['name']}"))
        info_layout.addWidget(QLabel(f"状态: {details['status']}"))
        info_layout.addWidget(QLabel(f"创建时间: {details['created_at']}"))
        info_group.setLayout(info_layout)
        content_layout.addWidget(info_group)
        
        # 进度信息
        if details['progress']:
            progress_group = QGroupBox("处理进度")
            progress_layout = QVBoxLayout()
            
            for step in details['progress']:
                step_widget = QWidget()
                step_layout = QHBoxLayout()
                
                # 状态图标
                status_icons = {
                    'pending': '⏳',
                    'in_progress': '🔄',
                    'completed': '✅',
                    'failed': '❌'
                }
                
                step_layout.addWidget(QLabel(f"{status_icons.get(step['status'], '')} {step['message']}"))
                
                # 进度条
                if step['progress'] and step['status'] == 'in_progress':
                    progress_bar = QProgressBar()
                    progress_bar.setValue(step['progress'])
                    step_layout.addWidget(progress_bar)
                    
                step_widget.setLayout(step_layout)
                progress_layout.addWidget(step_widget)
                
            progress_group.setLayout(progress_layout)
            content_layout.addWidget(progress_group)
            
        # 输入内容
        if details['input_text']:
            input_group = QGroupBox("输入内容")
            input_layout = QVBoxLayout()
            
            text_browser = QTextBrowser()
            text_browser.setPlainText(details['input_text'])
            text_browser.setMaximumHeight(100)
            input_layout.addWidget(text_browser)
            
            input_group.setLayout(input_layout)
            content_layout.addWidget(input_group)
            
        # 文件列表
        files_group = QGroupBox("生成的文件")
        files_layout = QVBoxLayout()
        
        # 音频文件
        if details['audio_files']:
            for audio in details['audio_files']:
                file_layout = QHBoxLayout()
                file_layout.addWidget(QLabel(f"🎵 {audio['file_name']} ({audio['file_size'] / 1024 / 1024:.1f} MB)"))
                
                if os.path.exists(audio['file_path']):
                    download_btn = QPushButton("下载")
                    download_btn.clicked.connect(lambda checked, path=audio['file_path']: self.download_file(path))
                    file_layout.addWidget(download_btn)
                    
                files_layout.addLayout(file_layout)
                
        # 字幕文件
        if details['subtitles']:
            for subtitle in details['subtitles']:
                if subtitle['srt_file_path'] and os.path.exists(subtitle['srt_file_path']):
                    file_layout = QHBoxLayout()
                    file_layout.addWidget(QLabel("📝 字幕文件.srt"))
                    
                    download_btn = QPushButton("下载")
                    download_btn.clicked.connect(lambda checked, path=subtitle['srt_file_path']: self.download_file(path))
                    file_layout.addWidget(download_btn)
                    
                    files_layout.addLayout(file_layout)
                    
        # 图片文件
        if details['images']:
            success_images = [img for img in details['images'] if img['status'] == 'success']
            if success_images:
                img_label = QLabel(f"🎨 生成的图片 ({len(success_images)} 张)")
                files_layout.addWidget(img_label)
                
                # 显示前几张图片的缩略图
                img_layout = QHBoxLayout()
                for i, image in enumerate(success_images[:5]):
                    if os.path.exists(image['file_path']):
                        pixmap = QPixmap(image['file_path'])
                        pixmap = pixmap.scaled(80, 60, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        
                        thumb_label = QLabel()
                        thumb_label.setPixmap(pixmap)
                        img_layout.addWidget(thumb_label)
                        
                img_layout.addStretch()
                files_layout.addLayout(img_layout)
                
        files_group.setLayout(files_layout)
        content_layout.addWidget(files_group)
        
        # 剪映导出
        if details['status'] == 'completed':
            export_group = QGroupBox("导出选项")
            export_layout = QVBoxLayout()
            
            export_btn = QPushButton("导出到剪映")
            export_btn.clicked.connect(lambda: self.export_to_jianying(project_id))
            export_layout.addWidget(export_btn)
            
            export_group.setLayout(export_layout)
            content_layout.addWidget(export_group)
            
        content_layout.addStretch()
        content.setLayout(content_layout)
        scroll.setWidget(content)
        
        self.detail_layout.addWidget(scroll)
        
        # 如果正在处理中，启动自动刷新
        if details['status'] == 'processing':
            if hasattr(self, 'refresh_timer'):
                self.refresh_timer.stop()
            self.refresh_timer = QTimer()
            self.refresh_timer.timeout.connect(lambda: self.load_project_detail(project_id))
            self.refresh_timer.start(2000)
        elif hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()
            
    def download_file(self, filepath):
        """下载文件"""
        if not os.path.exists(filepath):
            QMessageBox.warning(self, "提示", "文件不存在")
            return
            
        save_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存文件",
            os.path.basename(filepath)
        )
        
        if save_path:
            try:
                import shutil
                shutil.copy2(filepath, save_path)
                QMessageBox.information(self, "成功", "文件保存成功")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")
                
    def export_to_jianying(self, project_id):
        """导出到剪映"""
        try:
            from jianying_export_final import FinalJianyingExporter
            
            # 获取剪映路径
            jianying_path, ok = QFileDialog.getExistingDirectory(
                self,
                "选择剪映草稿文件夹",
                "",
                QFileDialog.ShowDirsOnly
            )
            
            if ok and jianying_path:
                exporter = FinalJianyingExporter(jianying_path)
                success = exporter.create_draft_from_project(project_id)
                
                if success:
                    QMessageBox.information(self, "成功", "剪映草稿创建成功！")
                else:
                    QMessageBox.critical(self, "错误", "创建剪映草稿失败")
                    
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

class NewProjectWidget(QWidget):
    """新建项目页面"""
    start_workflow = Signal(dict)
    
    def __init__(self, volcano_api, db, file_manager):
        super().__init__()
        self.volcano_api = volcano_api
        self.db = db
        self.file_manager = file_manager
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 项目名称
        name_group = QGroupBox("项目设置")
        name_layout = QVBoxLayout()
        
        name_layout.addWidget(QLabel("项目名称:"))
        self.name_edit = QLineEdit(f"项目_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        name_layout.addWidget(self.name_edit)
        
        name_group.setLayout(name_layout)
        layout.addWidget(name_group)
        
        # 模式选择
        mode_group = QGroupBox("选择模式")
        mode_layout = QVBoxLayout()
        
        self.text_mode_radio = QCheckBox("文本转语音")
        self.text_mode_radio.setChecked(True)
        self.text_mode_radio.toggled.connect(self.on_mode_changed)
        mode_layout.addWidget(self.text_mode_radio)
        
        self.audio_mode_radio = QCheckBox("上传音频文件")
        self.audio_mode_radio.toggled.connect(self.on_mode_changed)
        mode_layout.addWidget(self.audio_mode_radio)
        
        mode_group.setLayout(mode_layout)
        layout.addWidget(mode_group)
        
        # 输入区域
        self.input_stack = QStackedWidget()
        
        # 文本输入
        text_widget = QWidget()
        text_layout = QVBoxLayout()
        
        text_layout.addWidget(QLabel("输入文本:"))
        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText("在这里输入要转换的文本...")
        text_layout.addWidget(self.text_input)
        
        # 语音选择
        voice_layout = QHBoxLayout()
        voice_layout.addWidget(QLabel("音色:"))
        
        self.voice_combo = QComboBox()
        for category, voices in VOLCANO_VOICE_OPTIONS.items():
            for voice_id, voice_name in voices.items():
                self.voice_combo.addItem(f"{category} - {voice_name}", voice_id)
        voice_layout.addWidget(self.voice_combo)
        voice_layout.addStretch()
        
        text_layout.addLayout(voice_layout)
        text_widget.setLayout(text_layout)
        self.input_stack.addWidget(text_widget)
        
        # 音频上传
        audio_widget = QWidget()
        audio_layout = QVBoxLayout()
        
        audio_layout.addWidget(QLabel("选择音频文件:"))
        
        file_layout = QHBoxLayout()
        self.file_label = QLabel("未选择文件")
        file_layout.addWidget(self.file_label)
        
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_audio)
        file_layout.addWidget(browse_btn)
        file_layout.addStretch()
        
        audio_layout.addLayout(file_layout)
        audio_widget.setLayout(audio_layout)
        self.input_stack.addWidget(audio_widget)
        
        layout.addWidget(self.input_stack)
        
        # 开始按钮
        self.start_btn = QPushButton("开始处理")
        self.start_btn.clicked.connect(self.on_start)
        layout.addWidget(self.start_btn)
        
        layout.addStretch()
        self.setLayout(layout)
        
    def on_mode_changed(self, checked):
        if self.sender() == self.text_mode_radio and checked:
            self.audio_mode_radio.setChecked(False)
            self.input_stack.setCurrentIndex(0)
        elif self.sender() == self.audio_mode_radio and checked:
            self.text_mode_radio.setChecked(False)
            self.input_stack.setCurrentIndex(1)
            
    def browse_audio(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择音频文件",
            "",
            "音频文件 (*.mp3 *.mp4 *.mpeg *.mpga *.m4a *.wav *.webm)"
        )
        if file_path:
            self.file_label.setText(os.path.basename(file_path))
            self.audio_file_path = file_path
            
    def on_start(self):
        project_name = self.name_edit.text().strip()
        if not project_name:
            QMessageBox.warning(self, "提示", "请输入项目名称")
            return
            
        if self.text_mode_radio.isChecked():
            text_input = self.text_input.toPlainText().strip()
            if not text_input:
                QMessageBox.warning(self, "提示", "请输入文本")
                return
                
            project_id = self.db.create_project(project_name, text_input)
            
            config = {
                'project_id': project_id,
                'mode': 'text',
                'text_input': text_input,
                'voice_type': self.voice_combo.currentData(),
                'format': 'mp3',
                'sample_rate': 24000,
                'speed': 1.0,
                'volume': 1.0,
                'pitch': 1.0,
                'words_per_line': 20,
                'max_lines': 2,
                'use_punc': True,
                'use_itn': True,
                'custom_prompt': None
            }
        else:
            if not hasattr(self, 'audio_file_path'):
                QMessageBox.warning(self, "提示", "请选择音频文件")
                return
                
            input_info = f"上传音频: {os.path.basename(self.audio_file_path)}"
            project_id = self.db.create_project(project_name, input_info)
            
            config = {
                'project_id': project_id,
                'mode': 'audio',
                'audio_file_path': self.audio_file_path,
                'language': 'zh-CN',
                'caption_type': 'speech',
                'words_per_line': 20,
                'max_lines': 2,
                'use_punc': True,
                'use_itn': True,
                'custom_prompt': None
            }
            
        self.start_workflow.emit(config)
        
        # 清空输入
        self.text_input.clear()
        self.file_label.setText("未选择文件")
        if hasattr(self, 'audio_file_path'):
            delattr(self, 'audio_file_path')

class MainWindow(QMainWindow):
    """主窗口 - 简洁版"""
    def __init__(self):
        super().__init__()
        self.init_api()
        self.init_ui()
        
    def init_api(self):
        if not os.getenv("VOLCANO_APP_ID") or not os.getenv("VOLCANO_ACCESS_TOKEN"):
            QMessageBox.critical(None, "错误", "请配置火山引擎 API")
            sys.exit(1)
            
        self.volcano_api = VolcanoSpeechAPI(
            app_id=os.getenv("VOLCANO_APP_ID"),
            access_token=os.getenv("VOLCANO_ACCESS_TOKEN")
        )
        self.db = Database()
        self.file_manager = FileManager()
        
    def init_ui(self):
        self.setWindowTitle("AI音频字幕工具")
        self.setMinimumSize(1000, 700)
        
        # 中心Widget使用Tab
        self.tab_widget = QTabWidget()
        self.setCentralWidget(self.tab_widget)
        
        # 新建项目页面
        self.new_project_widget = NewProjectWidget(self.volcano_api, self.db, self.file_manager)
        self.new_project_widget.start_workflow.connect(self.start_workflow)
        self.tab_widget.addTab(self.new_project_widget, "新建项目")
        
        # 项目管理页面
        self.project_widget = ProjectManagementWidget(self.db, self.file_manager)
        self.tab_widget.addTab(self.project_widget, "项目管理")
        
        # 状态栏
        self.status_bar = self.statusBar()
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
    def start_workflow(self, config):
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        self.status_label.setText("正在处理...")
        
        # 切换到项目管理页面
        self.tab_widget.setCurrentIndex(1)
        
        # 根据模式创建工作线程
        if config['mode'] == 'text':
            from app import run_automatic_workflow
            voice_settings = {
                'voice_type': config['voice_type'],
                'format': config['format'],
                'sample_rate': config['sample_rate'],
                'speed': config['speed'],
                'volume': config['volume'],
                'pitch': config['pitch'],
                'words_per_line': config['words_per_line'],
                'max_lines': config['max_lines'],
                'use_punc': config['use_punc'],
                'use_itn': config['use_itn'],
                'custom_prompt': config['custom_prompt']
            }
            
            self.current_worker = AsyncWorker(
                run_automatic_workflow,
                config['project_id'],
                config['text_input'],
                voice_settings
            )
        else:
            with open(config['audio_file_path'], 'rb') as f:
                audio_content = f.read()
                
            import io
            audio_file_obj = io.BytesIO(audio_content)
            audio_file_obj.name = os.path.basename(config['audio_file_path'])
            
            audio_settings = {
                'words_per_line': config['words_per_line'],
                'max_lines': config['max_lines'],
                'language': config['language'],
                'caption_type': config['caption_type'],
                'use_punc': config['use_punc'],
                'use_itn': config['use_itn'],
                'custom_prompt': config['custom_prompt']
            }
            
            self.current_worker = AsyncWorker(
                run_audio_workflow,
                config['project_id'],
                audio_file_obj,
                audio_settings,
                self.db,
                self.file_manager,
                self.volcano_api
            )
            
        self.current_worker.task_completed.connect(self.on_workflow_completed)
        self.current_worker.start()
        
        # 刷新项目列表
        self.project_widget.refresh_list()
        
    @Slot(bool, str)
    def on_workflow_completed(self, success, message):
        self.progress_bar.setVisible(False)
        
        if success:
            self.status_label.setText("处理完成")
            QMessageBox.information(self, "成功", "处理完成！")
        else:
            self.status_label.setText("处理失败")
            QMessageBox.critical(self, "错误", f"处理失败：{message}")
            
        # 刷新项目列表
        self.project_widget.refresh_list()

def main():
    app = QApplication(sys.argv)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
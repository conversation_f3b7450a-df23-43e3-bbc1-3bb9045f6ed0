import streamlit as st
import requests
import time
from dotenv import load_dotenv
import os
import tempfile
from volcano_api import VolcanoSpeechAPI, convert_to_srt, convert_tts_to_srt, VOLCANO_VOICE_OPTIONS, get_ai_prompt_from_subtitles, generate_images_for_scenes
import base64
import asyncio
import json
from database import Database
from file_manager import FileManager
import threading
from datetime import datetime
from workflow_audio import run_audio_workflow
try:
    from jianying_export import JianyingExporter
    JIANYING_AVAILABLE = True
except ImportError:
    JIANYING_AVAILABLE = False
    print("警告: 剪映导出功能不可用")

load_dotenv()

st.set_page_config(
    page_title="AI音频字幕工具",
    page_icon="🎙️",
    layout="wide"
)

st.title("🎙️ AI音频字幕工具")
st.markdown("支持文本生成语音和音频提取字幕两种模式")

# 检查API配置
volcano_configured = os.getenv("VOLCANO_APP_ID") and os.getenv("VOLCANO_ACCESS_TOKEN")

if not volcano_configured:
    st.error("请配置火山引擎 API")
    st.info("在 .env 文件中设置 VOLCANO_APP_ID 和 VOLCANO_ACCESS_TOKEN")
    st.stop()

# 初始化API和管理器
volcano_api = VolcanoSpeechAPI(
    app_id=os.getenv("VOLCANO_APP_ID"),
    access_token=os.getenv("VOLCANO_ACCESS_TOKEN")
)

db = Database()
file_manager = FileManager()

# 自动工作流函数
async def run_automatic_workflow(project_id: int, text_input: str, voice_settings: dict):
    """运行自动化工作流程"""
    try:
        # 更新项目状态
        db.update_project_status(project_id, 'processing')
        
        # 步骤1: 文本转语音
        db.update_progress(project_id, 'text_to_speech', 'in_progress', 10, '正在生成语音...')
        
        tts_result = volcano_api.text_to_speech_complete(
            text=text_input,
            voice_type=voice_settings['voice_type'],
            format=voice_settings['format'],
            sample_rate=voice_settings['sample_rate'],
            speed=voice_settings['speed'],
            volume=voice_settings['volume'],
            pitch=voice_settings['pitch'],
            enable_subtitle=0
        )
        
        if not tts_result or not tts_result.get("audio_path"):
            db.update_progress(project_id, 'text_to_speech', 'failed', 0, '语音生成失败')
            db.update_project_status(project_id, 'failed')
            return False
        
        # 保存音频文件
        audio_path = file_manager.save_audio_file(
            project_id, 
            tts_result["audio_path"], 
            f"audio_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp3"
        )
        
        # 获取文件大小
        file_size = os.path.getsize(audio_path)
        
        # 保存到数据库
        audio_id = db.save_audio_file(
            project_id=project_id,
            file_path=audio_path,
            file_name=os.path.basename(audio_path),
            voice_type=voice_settings['voice_type'],
            format=voice_settings['format'],
            sample_rate=voice_settings['sample_rate'],
            speed=voice_settings['speed'],
            volume=voice_settings['volume'],
            pitch=voice_settings['pitch'],
            file_size=file_size
        )
        
        db.update_progress(project_id, 'text_to_speech', 'completed', 100, '语音生成成功')
        
        # 步骤2: 生成字幕
        db.update_progress(project_id, 'generate_subtitle', 'in_progress', 10, '正在生成字幕...')
        
        # 读取生成的音频文件
        with open(audio_path, 'rb') as f:
            audio_content = f.read()
        
        # 创建类似上传文件的对象
        import io
        audio_file_obj = io.BytesIO(audio_content)
        audio_file_obj.name = os.path.basename(audio_path)
        
        # 提交到火山引擎字幕生成API
        submit_result = volcano_api.submit_audio(
            audio_file=audio_file_obj,
            words_per_line=voice_settings.get('words_per_line', 20),
            max_lines=voice_settings.get('max_lines', 2),
            language="zh-CN",
            use_punc=voice_settings.get('use_punc', True),
            caption_type="speech"
        )
        
        if submit_result.get("code") != 0:
            db.update_progress(project_id, 'generate_subtitle', 'failed', 0, f'字幕生成提交失败: {submit_result.get("message")}')
            db.update_project_status(project_id, 'failed')
            return False
        
        task_id = submit_result.get("id")
        db.update_progress(project_id, 'generate_subtitle', 'in_progress', 50, f'字幕任务已提交，任务ID: {task_id}')
        
        # 查询结果
        result = volcano_api.query_result(task_id, blocking=True)
        
        if result.get("code") != 0:
            db.update_progress(project_id, 'generate_subtitle', 'failed', 0, f'字幕处理失败: {result.get("message")}')
            db.update_project_status(project_id, 'failed')
            return False
        
        # 保存字幕
        utterances = result.get("utterances", [])
        srt_content = convert_to_srt(utterances)
        srt_file_path = file_manager.save_subtitle_file(project_id, srt_content, "srt")
        
        # 保存字幕信息到数据库
        subtitle_id = db.save_subtitle(
            project_id=project_id,
            audio_id=audio_id,
            srt_file_path=srt_file_path,
            srt_content=srt_content,
            json_content=json.dumps(utterances, ensure_ascii=False),
            volcano_task_id=task_id
        )
        
        db.update_progress(project_id, 'generate_subtitle', 'completed', 100, '字幕生成成功')
        
        # 步骤3: 生成AI提示词
        db.update_progress(project_id, 'generate_ai_prompt', 'in_progress', 10, '正在生成AI分镜提示词...')
        
        # 准备字幕数据
        subtitle_data = []
        for utterance in utterances:
            subtitle_data.append({
                "content": utterance['text'],
                "start": int(utterance.get('start_time', utterance.get('begin_time', 0)) * 1000),
                "end": int(utterance.get('end_time', utterance.get('end_time', 0)) * 1000)
            })
        
        # 生成AI提示词
        custom_prompt = voice_settings.get('custom_prompt')
        ai_prompt_result = await get_ai_prompt_from_subtitles(subtitle_data, custom_prompt=custom_prompt)
        
        if not ai_prompt_result:
            db.update_progress(project_id, 'generate_ai_prompt', 'failed', 0, 'AI提示词生成失败')
            db.update_project_status(project_id, 'failed')
            return False
        
        # 保存提示词文件
        prompts_json = json.dumps(ai_prompt_result, ensure_ascii=False, indent=2)
        prompts_file_path = file_manager.save_prompt_file(project_id, prompts_json)
        
        # 保存到数据库
        prompts_with_index = []
        for i, prompt in enumerate(ai_prompt_result):
            prompt['scene_index'] = i + 1
            prompts_with_index.append(prompt)
        
        db.save_ai_prompts(project_id, subtitle_id, prompts_with_index, prompts_file_path)
        
        db.update_progress(project_id, 'generate_ai_prompt', 'completed', 100, f'成功生成{len(ai_prompt_result)}个场景提示词')
        
        # 步骤4: 生成图片
        db.update_progress(project_id, 'generate_images', 'in_progress', 10, f'正在生成{len(ai_prompt_result)}个场景的图片...')
        
        # 获取DMXAPI密钥
        dmx_api_key = os.getenv('DMXAPI_KEY')
        if not dmx_api_key:
            st.warning("未配置DMXAPI密钥，使用默认密钥")
        
        # 生成图片
        image_results = await generate_images_for_scenes(
            scenes=ai_prompt_result,
            api_key=dmx_api_key,
            model="flux-schnell"
        )
        
        # 保存图片信息
        success_count = 0
        for result in image_results:
            if result['success']:
                # 保存图片到项目目录
                saved_path = file_manager.save_image_file(
                    project_id,
                    result['local_path'],
                    result['scene_index'],
                    result['start'],
                    result['end']
                )
                
                # 保存到数据库
                db.save_image(
                    project_id=project_id,
                    scene_index=result['scene_index'],
                    start_time=result['start'] / 1000.0,
                    end_time=result['end'] / 1000.0,
                    file_path=saved_path,
                    file_name=os.path.basename(saved_path),
                    image_url=result.get('image_url'),
                    prompt_used=result['prompt'],
                    model_used="flux-schnell",
                    status='success'
                )
                success_count += 1
            else:
                # 记录失败的图片
                db.save_image(
                    project_id=project_id,
                    scene_index=result['scene_index'],
                    start_time=result['start'] / 1000.0,
                    end_time=result['end'] / 1000.0,
                    file_path='',
                    file_name='',
                    prompt_used=result['prompt'],
                    model_used="flux-schnell",
                    status='failed',
                    error_message=result.get('error', '未知错误')
                )
        
        db.update_progress(project_id, 'generate_images', 'completed', 100, 
                          f'图片生成完成！成功 {success_count}/{len(image_results)} 个')
        
        # 更新项目状态
        db.update_project_status(project_id, 'completed')
        
        # 清理临时文件
        if os.path.exists(tts_result["audio_path"]):
            os.unlink(tts_result["audio_path"])
        
        return True
        
    except Exception as e:
        db.update_project_status(project_id, 'failed')
        db.update_progress(project_id, 'generate_images', 'failed', 0, f'发生错误: {str(e)}')
        st.error(f"自动工作流程出错: {str(e)}")
        return False

# 使用侧边栏进行导航
st.sidebar.title("导航")
page = st.sidebar.radio("选择页面", ["新建项目", "项目列表", "项目详情"])

if page == "新建项目":
    st.subheader("📝 创建新项目")
    
    # 选择模式
    mode = st.radio(
        "选择使用模式",
        ["输入文本生成语音", "上传音频文件"]
    )
    
    # 项目名称
    project_name = st.text_input("项目名称", value=f"项目_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    
    if mode == "输入文本生成语音":
        # 输入文本
        text_input = st.text_area(
            "请输入要转换的文本",
            height=200,
            placeholder="在这里输入文本...",
            max_chars=50000
        )
        
        # 语音设置
        with st.expander("🎤 语音设置", expanded=True):
            col1, col2 = st.columns(2)
            
            with col1:
                voice_category = st.selectbox("音色类别", list(VOLCANO_VOICE_OPTIONS.keys()))
                voice_type = st.selectbox(
                    "选择音色",
                    options=list(VOLCANO_VOICE_OPTIONS[voice_category].keys()),
                    format_func=lambda x: VOLCANO_VOICE_OPTIONS[voice_category][x]
                )
                
                format_option = st.selectbox(
                    "音频格式",
                    options=["mp3", "wav", "pcm", "ogg_opus"],
                    index=0
                )
                
            with col2:
                speed = st.slider("语速", 0.2, 3.0, 1.0, 0.1)
                volume = st.slider("音量", 0.1, 3.0, 1.0, 0.1)
                pitch = st.slider("音调", 0.1, 3.0, 1.0, 0.1)
                
                sample_rate = st.selectbox(
                    "采样率",
                    options=[8000, 16000, 24000],
                    index=2,
                    format_func=lambda x: f"{x} Hz"
                )
        
        # 字幕设置
        with st.expander("⚙️ 字幕设置"):
            col3, col4 = st.columns(2)
            with col3:
                words_per_line = st.number_input("每行最多字数", min_value=10, max_value=50, value=20)
                max_lines = st.number_input("每屏最多行数", min_value=1, max_value=5, value=2)
            with col4:
                use_punc = st.checkbox("添加标点符号", value=True)
                use_itn = st.checkbox("数字转换", value=True)

        # AI分镜提示词设置
        with st.expander("🎬 AI分镜提示词设置"):
            use_custom_prompt = st.checkbox("使用自定义提示词", value=False, help="勾选后可以输入自定义的AI分镜提示词模板，否则使用默认的未来科幻风格")

            if use_custom_prompt:
                custom_prompt = st.text_area(
                    "自定义提示词模板",
                    height=300,
                    placeholder="""请输入您的自定义提示词模板，例如：

你是一名专业的分镜设计师，请将字幕转换为动漫风格的AI图像提示词。

要求：
1. 输出JSON数组格式
2. 每个元素包含 start, end, img_prompt 字段
3. img_prompt 为英文描述，风格为日式动漫

示例输出格式：
[
  {
    "start": 1000,
    "end": 3000,
    "img_prompt": "anime style illustration, cute girl with big eyes, colorful background"
  }
]

注意：字幕数据会自动添加到您的提示词末尾，无需在此处包含。""",
                    help="提示词模板应该包含输出格式要求，字幕数据会自动添加到末尾"
                )
            else:
                custom_prompt = None
                st.info("💡 将使用默认的未来科幻宣传风格提示词模板")
        
        if st.button("🚀 开始自动处理", type="primary", use_container_width=True):
            if not text_input.strip():
                st.error("请输入文本")
            else:
                # 创建项目
                project_id = db.create_project(project_name, text_input)
                st.success(f"项目创建成功！项目ID: {project_id}")
                
                # 准备语音设置
                voice_settings = {
                    'voice_type': voice_type,
                    'format': format_option,
                    'sample_rate': sample_rate,
                    'speed': speed,
                    'volume': volume,
                    'pitch': pitch,
                    'words_per_line': words_per_line,
                    'max_lines': max_lines,
                    'use_punc': use_punc,
                    'use_itn': use_itn,
                    'custom_prompt': custom_prompt if use_custom_prompt else None
                }
                
                # 保存项目ID到session state
                st.session_state['current_project_id'] = project_id
                
                # 显示进度信息
                progress_container = st.container()
                
                with progress_container:
                    st.info("正在自动处理，请稍候...")
                    
                    # 启动异步任务
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    success = loop.run_until_complete(
                        run_automatic_workflow(project_id, text_input, voice_settings)
                    )
                    
                    if success:
                        st.success("✅ 所有处理步骤已完成！")
                        st.info("请前往'项目详情'页面查看结果")
                    else:
                        st.error("❌ 处理过程中出现错误，请查看项目详情了解具体信息")
    
    else:  # 上传音频文件模式
        # 上传音频文件
        uploaded_file = st.file_uploader(
            "选择音频文件",
            type=['mp3', 'mp4', 'mpeg', 'mpga', 'm4a', 'wav', 'webm'],
            help="支持 MP3, WAV, M4A 等格式"
        )
        
        if uploaded_file is not None:
            st.success(f"已选择文件: {uploaded_file.name}")
            
            file_size_mb = uploaded_file.size / (1024 * 1024)
            st.info(f"文件大小: {file_size_mb:.1f}MB")
        
        # 字幕设置
        with st.expander("⚙️ 字幕设置", expanded=True):
            col_a, col_b = st.columns(2)
            with col_a:
                words_per_line = st.number_input("每行最多字数", min_value=10, max_value=50, value=20)
                max_lines = st.number_input("每屏最多行数", min_value=1, max_value=5, value=2)
                language = st.selectbox(
                    "语言",
                    options=["zh-CN", "en-US", "ja-JP", "ko-KR"],
                    format_func=lambda x: {
                        "zh-CN": "中文普通话",
                        "en-US": "英语（美国）",
                        "ja-JP": "日语",
                        "ko-KR": "韩语"
                    }.get(x, x)
                )
            with col_b:
                caption_type = st.selectbox(
                    "识别类型",
                    options=["speech", "auto", "singing"],
                    format_func=lambda x: {
                        "speech": "只识别说话",
                        "auto": "说话和唱歌都识别",
                        "singing": "只识别唱歌"
                    }.get(x, x)
                )
                use_punc = st.checkbox("添加标点符号", value=True)
                use_itn = st.checkbox("数字转换", value=True)

        # AI分镜提示词设置
        with st.expander("🎬 AI分镜提示词设置"):
            use_custom_prompt = st.checkbox("使用自定义提示词", value=False, help="勾选后可以输入自定义的AI分镜提示词模板，否则使用默认的未来科幻风格")

            if use_custom_prompt:
                custom_prompt = st.text_area(
                    "自定义提示词模板",
                    height=300,
                    placeholder="""请输入您的自定义提示词模板，例如：

你是一名专业的分镜设计师，请将字幕转换为动漫风格的AI图像提示词。

要求：
1. 输出JSON数组格式
2. 每个元素包含 start, end, img_prompt 字段
3. img_prompt 为英文描述，风格为日式动漫

示例输出格式：
[
  {
    "start": 1000,
    "end": 3000,
    "img_prompt": "anime style illustration, cute girl with big eyes, colorful background"
  }
]

注意：字幕数据会自动添加到您的提示词末尾，无需在此处包含。""",
                    help="提示词模板应该包含输出格式要求，字幕数据会自动添加到末尾"
                )
            else:
                custom_prompt = None
                st.info("💡 将使用默认的未来科幻宣传风格提示词模板")
        
        if st.button("🚀 开始自动处理", type="primary", use_container_width=True):
            if uploaded_file is None:
                st.error("请选择音频文件")
            else:
                # 创建项目
                input_info = f"上传音频: {uploaded_file.name}"
                project_id = db.create_project(project_name, input_info)
                st.success(f"项目创建成功！项目ID: {project_id}")
                
                # 更新进度步骤（音频上传模式不需要TTS步骤）
                db.conn.execute(
                    "UPDATE project_progress SET step_name = 'save_audio', message = '保存音频文件' WHERE project_id = ? AND step_name = 'text_to_speech'",
                    (project_id,)
                )
                db.conn.commit()
                
                # 准备设置
                audio_settings = {
                    'words_per_line': words_per_line,
                    'max_lines': max_lines,
                    'language': language,
                    'caption_type': caption_type,
                    'use_punc': use_punc and caption_type == "speech",
                    'use_itn': use_itn,
                    'custom_prompt': custom_prompt if use_custom_prompt else None
                }
                
                # 保存项目ID到session state
                st.session_state['current_project_id'] = project_id
                
                # 显示进度信息
                progress_container = st.container()
                
                with progress_container:
                    st.info("正在自动处理，请稍候...")
                    
                    # 启动异步任务
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    success = loop.run_until_complete(
                        run_audio_workflow(project_id, uploaded_file, audio_settings, db, file_manager, volcano_api)
                    )
                    
                    if success:
                        st.success("✅ 所有处理步骤已完成！")
                        st.info("请前往'项目详情'页面查看结果")
                    else:
                        st.error("❌ 处理过程中出现错误，请查看项目详情了解具体信息")

elif page == "项目列表":
    st.subheader("📁 项目列表")
    
    # 获取所有项目
    projects = db.get_all_projects()
    
    if projects:
        # 创建表格显示项目
        for project in projects:
            col1, col2, col3, col4 = st.columns([3, 2, 2, 1])
            
            with col1:
                st.write(f"**{project['name']}**")
            with col2:
                status_map = {
                    'pending': '⏳ 待处理',
                    'processing': '🔄 处理中',
                    'completed': '✅ 已完成',
                    'failed': '❌ 失败'
                }
                st.write(status_map.get(project['status'], project['status']))
            with col3:
                st.write(f"创建时间: {project['created_at'][:16]}")
            with col4:
                if st.button("查看", key=f"view_{project['id']}"):
                    st.session_state['selected_project_id'] = project['id']
                    st.sidebar.info(f"已选择项目 {project['name']}，请点击左侧'项目详情'查看")
    else:
        st.info("暂无项目")

elif page == "项目详情":
    st.subheader("📋 项目详情")
    
    # 获取要显示的项目ID
    selected_project_id = st.session_state.get('selected_project_id') or st.session_state.get('current_project_id')
    
    if selected_project_id:
        # 获取项目详情
        project_details = db.get_project_details(selected_project_id)
        
        # 显示项目基本信息
        st.write(f"**项目名称:** {project_details['name']}")
        st.write(f"**状态:** {project_details['status']}")
        st.write(f"**创建时间:** {project_details['created_at']}")
        
        # 显示进度
        st.subheader("进度跟踪")
        progress_data = project_details['progress']
        
        for step in progress_data:
            col1, col2, col3 = st.columns([2, 1, 4])
            
            with col1:
                st.write(step['message'])
            with col2:
                status_icon = {
                    'pending': '⏳',
                    'in_progress': '🔄',
                    'completed': '✅',
                    'failed': '❌'
                }.get(step['status'], '')
                st.write(f"{status_icon} {step['status']}")
            with col3:
                if step['progress']:
                    st.progress(step['progress'] / 100)
        
        # 显示输入文本
        with st.expander("输入文本"):
            st.text_area("输入文本", project_details['input_text'], height=200, disabled=True, label_visibility="collapsed")
        
        # 显示音频文件
        if project_details['audio_files']:
            st.subheader("🎵 音频文件")
            for audio in project_details['audio_files']:
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.write(f"**{audio['file_name']}** ({audio['file_size'] / 1024 / 1024:.1f} MB)")
                    if os.path.exists(audio['file_path']):
                        with open(audio['file_path'], 'rb') as f:
                            st.audio(f.read(), format=f'audio/{audio["format"]}')
                with col2:
                    if os.path.exists(audio['file_path']):
                        with open(audio['file_path'], 'rb') as f:
                            st.download_button(
                                "下载",
                                f.read(),
                                file_name=audio['file_name'],
                                mime=f'audio/{audio["format"]}'
                            )
        
        # 显示字幕
        if project_details['subtitles']:
            st.subheader("📝 字幕文件")
            for subtitle in project_details['subtitles']:
                if subtitle['srt_file_path'] and os.path.exists(subtitle['srt_file_path']):
                    with st.expander("查看字幕内容"):
                        st.text_area("字幕内容", subtitle['srt_content'], height=300, disabled=True, label_visibility="collapsed")
                    
                    st.download_button(
                        "下载 SRT 字幕",
                        subtitle['srt_content'],
                        file_name=os.path.basename(subtitle['srt_file_path']),
                        mime="text/plain"
                    )
        
        # 显示AI提示词
        if project_details['ai_prompts']:
            st.subheader("🎬 AI分镜提示词")
            
            # 按场景显示
            for prompt in project_details['ai_prompts']:
                with st.expander(f"场景 {prompt['scene_index']}: {prompt['start_time']:.1f}s - {prompt['end_time']:.1f}s"):
                    st.text_area(
                        "英文提示词",
                        prompt['prompt_text'],
                        height=100,
                        disabled=True,
                        label_visibility="visible"
                    )
            
            # 下载所有提示词
            if project_details['ai_prompts'] and project_details['ai_prompts'][0].get('json_file_path'):
                json_path = project_details['ai_prompts'][0]['json_file_path']
                if os.path.exists(json_path):
                    with open(json_path, 'r', encoding='utf-8') as f:
                        st.download_button(
                            "下载所有提示词 (JSON)",
                            f.read(),
                            file_name=os.path.basename(json_path),
                            mime="application/json"
                        )
        
        # 显示生成的图片
        if project_details['images']:
            st.subheader("🎨 生成的分镜图片")
            
            success_images = [img for img in project_details['images'] if img['status'] == 'success']
            failed_images = [img for img in project_details['images'] if img['status'] == 'failed']
            
            if success_images:
                # 使用列布局显示图片
                cols = st.columns(3)
                for i, image in enumerate(success_images):
                    with cols[i % 3]:
                        st.write(f"**场景 {image['scene_index']}**")
                        st.write(f"{image['start_time']:.1f}s - {image['end_time']:.1f}s")
                        
                        if os.path.exists(image['file_path']):
                            st.image(image['file_path'], use_container_width=True)
                            
                            with open(image['file_path'], 'rb') as f:
                                st.download_button(
                                    "下载",
                                    f.read(),
                                    file_name=image['file_name'],
                                    mime="image/png",
                                    key=f"download_{image['id']}"
                                )
            
            if failed_images:
                with st.expander(f"失败的图片 ({len(failed_images)})"):
                    for image in failed_images:
                        st.error(f"场景 {image['scene_index']}: {image['error_message']}")
        
        # 刷新按钮
        if project_details['status'] == 'processing':
            if st.button("🔄 刷新状态"):
                st.rerun()
        
        # 导出到剪映
        if project_details['status'] == 'completed' and JIANYING_AVAILABLE:
            st.subheader("🎬 导出到剪映")
            
            # 检查是否已经创建过草稿
            if project_details.get('jianying_draft_path'):
                st.success(f"已创建剪映草稿: {project_details['jianying_draft_path']}")
            
            # 剪映草稿路径设置
            with st.expander("剪映设置", expanded=True):
                # 从session state获取或使用默认值
                default_path = st.session_state.get('jianying_drafts_path', '')
                
                jianying_drafts_path = st.text_input(
                    "剪映草稿文件夹路径",
                    value=default_path,
                    help="通常在剪映的'全局设置'-'草稿位置'中可以找到，Mac上一般为 /Users/<USER>/Movies/JianyingPro Drafts"
                )
                
                if jianying_drafts_path:
                    st.session_state['jianying_drafts_path'] = jianying_drafts_path
                
                draft_name = st.text_input(
                    "草稿名称（可选）",
                    placeholder=f"{project_details['name']}_草稿",
                    help="不填则自动生成"
                )
            
            # 创建剪映草稿
            st.info("💫 新版本包含丰富的动画效果：第一张图片淡入，最后一张图片淡出，中间图片使用各种组合动画")
            if st.button("🎬 创建剪映草稿", type="primary", use_container_width=True):
                if not jianying_drafts_path:
                    st.error("请输入剪映草稿文件夹路径")
                elif not os.path.exists(jianying_drafts_path):
                    st.error("剪映草稿文件夹路径不存在")
                else:
                    with st.spinner("正在创建剪映草稿..."):
                        try:
                            from jianying_export_final import FinalJianyingExporter

                            exporter = FinalJianyingExporter(jianying_drafts_path)
                            success = exporter.create_draft_from_project(
                                selected_project_id,
                                draft_name if draft_name else None
                            )

                            if success:
                                st.success("✅ 剪映草稿创建成功！")
                                st.info("🎬 请打开剪映查看草稿")
                                st.balloons()
                                st.rerun()
                            else:
                                st.error("❌ 创建剪映草稿失败，请查看控制台输出")
                        except Exception as e:
                            st.error(f"创建剪映草稿时出错: {str(e)}")
                            st.exception(e)

            col1, col2 = st.columns(2)

            with col1:
                # 导出视频功能（需要剪映支持）
                if st.button("📹 导出视频", use_container_width=True, disabled=True):
                    st.info("此功能需要剪映6.0及以下版本支持")

            with col2:
                # 删除草稿
                if st.button("🗑️ 删除草稿", use_container_width=True):
                    if project_details.get('jianying_draft_path'):
                        draft_path = project_details['jianying_draft_path']
                        if os.path.exists(draft_path):
                            try:
                                import shutil
                                shutil.rmtree(draft_path)

                                # 更新数据库
                                db.conn.execute(
                                    "UPDATE projects SET jianying_draft_path = NULL WHERE id = ?",
                                    (selected_project_id,)
                                )
                                db.conn.commit()

                                st.success("✅ 草稿已删除")
                                st.rerun()
                            except Exception as e:
                                st.error(f"删除草稿失败: {str(e)}")
                        else:
                            st.warning("草稿文件夹不存在")
                    else:
                        st.info("没有草稿可删除")
    else:
        st.info("请从项目列表中选择一个项目查看详情")

# 侧边栏信息
st.sidebar.markdown("---")
st.sidebar.markdown("### 功能说明")
st.sidebar.markdown("""
**自动化工作流程:**
1. 输入文本内容/上传音频
2. 自动生成语音（火山引擎TTS）
3. 自动提取字幕
4. 自动生成AI分镜提示词
5. 自动生成分镜图片
6. 一键生成剪映草稿

**数据管理:**
- 所有数据保存到SQLite数据库
- 文件按项目组织存储
- 支持查看历史项目
- 支持下载所有生成的文件

**剪映导出:**
- 自动组合音频、字幕、图片
- 生成可编辑的剪映草稿
- 支持剪映5.9及以上版本
""")

st.sidebar.markdown("### API 状态")
if volcano_configured:
    st.sidebar.success("✅ 火山引擎 API 已配置")
else:
    st.sidebar.error("❌ 火山引擎 API 未配置")

if os.getenv('DMXAPI_KEY'):
    st.sidebar.success("✅ DMXAPI 已配置")
else:
    st.sidebar.warning("⚠️ DMXAPI 未配置（使用默认密钥）")

# 清理缓存按钮
if st.sidebar.button("🗑️ 清理临时文件"):
    file_manager.cleanup_temp_files()
    st.sidebar.success("临时文件已清理")
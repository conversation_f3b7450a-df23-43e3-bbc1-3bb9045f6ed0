"""
YouTube API认证和账号管理模块
处理OAuth2认证流程，管理多账号凭证
"""

import os
import json
import pickle
import logging
from typing import Optional, Dict, Any
from pathlib import Path
import threading
from datetime import datetime, timedelta

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

logger = logging.getLogger(__name__)

# YouTube API作用域
SCOPES = [
    'https://www.googleapis.com/auth/youtube.upload',
    'https://www.googleapis.com/auth/youtube',
    'https://www.googleapis.com/auth/youtube.readonly'
]

class YouTubeAuthManager:
    """YouTube认证管理器"""
    
    def __init__(self, credentials_dir: str = "credentials"):
        self.credentials_dir = Path(credentials_dir)
        self.credentials_dir.mkdir(exist_ok=True)
        self._services = {}  # 缓存已创建的服务实例
        self._lock = threading.Lock()
    
    def authenticate_account(self, account_name: str, client_secrets_file: str) -> Dict[str, Any]:
        """
        认证YouTube账号
        
        Args:
            account_name: 账号名称（用于识别）
            client_secrets_file: OAuth2客户端密钥文件路径
            
        Returns:
            包含认证信息的字典
        """
        if not os.path.exists(client_secrets_file):
            raise FileNotFoundError(f"客户端密钥文件不存在: {client_secrets_file}")
        
        # 凭证文件路径
        token_file = self.credentials_dir / f"{account_name}_token.pickle"
        
        creds = None
        
        # 尝试加载现有凭证
        if token_file.exists():
            with open(token_file, 'rb') as token:
                creds = pickle.load(token)
                logger.info(f"已加载账号 {account_name} 的现有凭证")
        
        # 检查凭证是否需要刷新
        if creds and creds.expired and creds.refresh_token:
            try:
                creds.refresh(Request())
                logger.info(f"已刷新账号 {account_name} 的凭证")
            except Exception as e:
                logger.error(f"刷新凭证失败: {str(e)}")
                creds = None
        
        # 如果没有有效凭证，执行OAuth流程
        if not creds or not creds.valid:
            flow = InstalledAppFlow.from_client_secrets_file(
                client_secrets_file, SCOPES
            )
            
            # 使用本地服务器接收授权码
            creds = flow.run_local_server(
                port=0,
                success_message='认证成功！您可以关闭此窗口。'
            )
            
            # 保存凭证
            with open(token_file, 'wb') as token:
                pickle.dump(creds, token)
            
            logger.info(f"账号 {account_name} 认证成功并保存凭证")
        
        # 获取频道信息
        try:
            service = build('youtube', 'v3', credentials=creds)
            response = service.channels().list(
                part='snippet,statistics',
                mine=True
            ).execute()
            
            if 'items' in response and response['items']:
                channel = response['items'][0]
                return {
                    'account_name': account_name,
                    'credentials_path': str(token_file),
                    'channel_id': channel['id'],
                    'channel_name': channel['snippet']['title'],
                    'channel_description': channel['snippet'].get('description', ''),
                    'subscriber_count': channel['statistics'].get('subscriberCount', '0'),
                    'video_count': channel['statistics'].get('videoCount', '0'),
                    'view_count': channel['statistics'].get('viewCount', '0')
                }
            else:
                raise Exception("无法获取频道信息")
                
        except Exception as e:
            logger.error(f"获取频道信息失败: {str(e)}")
            raise
    
    def load_credentials(self, credentials_path: str) -> Optional[Credentials]:
        """加载保存的凭证"""
        try:
            if os.path.exists(credentials_path):
                with open(credentials_path, 'rb') as token:
                    creds = pickle.load(token)
                    
                    # 检查是否需要刷新
                    if creds.expired and creds.refresh_token:
                        creds.refresh(Request())
                        
                        # 保存刷新后的凭证
                        with open(credentials_path, 'wb') as token:
                            pickle.dump(creds, token)
                    
                    return creds
        except Exception as e:
            logger.error(f"加载凭证失败: {str(e)}")
        
        return None
    
    def get_youtube_service(self, credentials_path: str):
        """获取YouTube API服务实例"""
        with self._lock:
            # 检查缓存
            if credentials_path in self._services:
                return self._services[credentials_path]
            
            # 加载凭证
            creds = self.load_credentials(credentials_path)
            if not creds:
                raise ValueError(f"无法加载凭证: {credentials_path}")
            
            # 创建服务实例
            service = build('youtube', 'v3', credentials=creds, cache_discovery=False)
            
            # 缓存服务实例
            self._services[credentials_path] = service
            
            return service
    
    def revoke_credentials(self, account_name: str) -> bool:
        """撤销账号凭证"""
        token_file = self.credentials_dir / f"{account_name}_token.pickle"
        
        try:
            if token_file.exists():
                # 加载凭证
                with open(token_file, 'rb') as token:
                    creds = pickle.load(token)
                
                # 撤销凭证
                if creds and creds.valid:
                    import requests
                    requests.post(
                        'https://oauth2.googleapis.com/revoke',
                        params={'token': creds.token},
                        headers={'content-type': 'application/x-www-form-urlencoded'}
                    )
                
                # 删除凭证文件
                token_file.unlink()
                
                # 从缓存中移除
                with self._lock:
                    if str(token_file) in self._services:
                        del self._services[str(token_file)]
                
                logger.info(f"已撤销账号 {account_name} 的凭证")
                return True
                
        except Exception as e:
            logger.error(f"撤销凭证失败: {str(e)}")
            return False
    
    def check_quota_usage(self, service) -> Dict[str, Any]:
        """
        检查配额使用情况
        注意：YouTube API v3没有直接的配额查询接口，这里通过简单请求估算
        """
        try:
            # 执行一个低成本的API调用
            response = service.channels().list(
                part='id',
                mine=True
            ).execute()
            
            # YouTube每日配额为10,000单位
            # 不同操作消耗不同的配额单位
            # 上传视频大约消耗1600单位
            return {
                'daily_limit': 10000,
                'estimated_upload_cost': 1600,
                'estimated_uploads_remaining': 6,  # 保守估计
                'note': '配额使用为估算值'
            }
            
        except HttpError as e:
            if e.resp.status == 403:
                return {
                    'daily_limit': 10000,
                    'error': '可能已达到配额限制',
                    'message': str(e)
                }
            raise
    
    def validate_account(self, credentials_path: str) -> bool:
        """验证账号凭证是否有效"""
        try:
            service = self.get_youtube_service(credentials_path)
            
            # 尝试获取频道信息
            response = service.channels().list(
                part='id',
                mine=True
            ).execute()
            
            return 'items' in response
            
        except Exception as e:
            logger.error(f"验证账号失败: {str(e)}")
            return False


class YouTubeQuotaTracker:
    """YouTube API配额跟踪器"""
    
    def __init__(self):
        self.quota_costs = {
            'videos.insert': 1600,  # 上传视频
            'videos.update': 50,    # 更新视频信息
            'videos.delete': 50,    # 删除视频
            'channels.list': 1,     # 列出频道
            'videos.list': 1,       # 列出视频
            'playlists.insert': 50, # 创建播放列表
            'playlistItems.insert': 50,  # 添加到播放列表
        }
        self._usage = {}  # 账号ID -> 使用量
        self._last_reset = {}  # 账号ID -> 上次重置时间
        self._lock = threading.Lock()
    
    def track_usage(self, account_id: int, operation: str, count: int = 1):
        """记录配额使用"""
        with self._lock:
            # 检查是否需要重置计数
            now = datetime.now()
            if account_id not in self._last_reset or \
               (now - self._last_reset[account_id]) > timedelta(days=1):
                self._usage[account_id] = 0
                self._last_reset[account_id] = now
            
            # 计算消耗
            cost = self.quota_costs.get(operation, 0) * count
            self._usage[account_id] = self._usage.get(account_id, 0) + cost
            
            return self._usage[account_id]
    
    def get_usage(self, account_id: int) -> int:
        """获取当前使用量"""
        with self._lock:
            # 检查是否需要重置
            now = datetime.now()
            if account_id in self._last_reset and \
               (now - self._last_reset[account_id]) > timedelta(days=1):
                self._usage[account_id] = 0
                self._last_reset[account_id] = now
            
            return self._usage.get(account_id, 0)
    
    def can_upload(self, account_id: int) -> bool:
        """检查是否还能上传视频"""
        usage = self.get_usage(account_id)
        # 保留一些余量，不要用满配额
        return usage + self.quota_costs['videos.insert'] <= 9000  # 留1000余量
    
    def estimate_remaining_uploads(self, account_id: int) -> int:
        """估算剩余可上传视频数"""
        usage = self.get_usage(account_id)
        remaining = max(0, 9000 - usage)  # 留1000余量
        return remaining // self.quota_costs['videos.insert']
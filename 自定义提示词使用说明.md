# AI分镜提示词自定义功能使用说明

## 功能概述

现在您可以在创建新项目时自定义AI分镜提示词模板，而不再局限于固定的未来科幻风格。这个功能让您能够根据不同的视频内容和风格需求，生成更符合预期的AI图像提示词。

## 如何使用

### 1. 创建新项目时的设置

在"新建项目"页面中，您会看到一个新的设置区域：

**🎬 AI分镜提示词设置**

- **使用自定义提示词**: 勾选此选项来启用自定义提示词功能
- **自定义提示词模板**: 当勾选上述选项后，会出现一个大文本框供您输入自定义模板

### 2. 自定义提示词模板编写指南

#### 基本要求
您的自定义提示词模板必须包含以下要素：

1. **明确的角色定义**: 告诉AI它是什么角色（如分镜设计师）
2. **输出格式要求**: 必须要求输出JSON数组格式
3. **字段说明**: 说明每个JSON对象应包含的字段
4. **风格描述**: 描述您想要的图像风格

#### 必需的JSON格式
输出必须是以下格式的JSON数组：
```json
[
  {
    "start": 起始时间（毫秒）,
    "end": 结束时间（毫秒）,
    "img_prompt": "英文图像描述提示词"
  }
]
```

#### 示例模板

**动漫风格模板：**
```
你是一名专业的分镜设计师，请将字幕转换为日式动漫风格的AI图像提示词。

要求：
1. 输出JSON数组格式
2. 每个元素包含 start, end, img_prompt 字段
3. img_prompt 为英文描述，风格为日式动漫
4. 画面要体现动漫特有的夸张表现和鲜艳色彩

示例输出格式：
[
  {
    "start": 1000,
    "end": 3000,
    "img_prompt": "anime style illustration, cute character with big expressive eyes, vibrant colors, dynamic pose"
  }
]

请严格按照JSON格式输出，不要添加任何解释文字。
```

**商业广告风格模板：**
```
你是一名专业的广告分镜设计师，请将字幕转换为高端商业广告风格的AI图像提示词。

要求：
1. 输出JSON数组格式
2. 每个元素包含 start, end, img_prompt 字段
3. img_prompt 为英文描述，风格为商业广告
4. 画面要体现专业、高端、现代的商业氛围

示例输出格式：
[
  {
    "start": 1000,
    "end": 3000,
    "img_prompt": "professional business advertisement, clean modern design, corporate atmosphere, high-end product showcase"
  }
]

请严格按照JSON格式输出，不要添加任何解释文字。
```

### 3. 注意事项

1. **字幕数据自动添加**: 您无需在模板中包含字幕数据，系统会自动将字幕信息添加到您的提示词末尾

2. **JSON格式严格要求**: 必须要求AI输出严格的JSON格式，否则可能导致解析失败

3. **英文提示词**: img_prompt字段建议使用英文，因为大多数AI图像生成模型对英文支持更好

4. **风格一致性**: 在模板中明确描述您想要的视觉风格，确保生成的所有场景保持一致

### 4. 默认模板

如果不勾选"使用自定义提示词"，系统将使用默认的未来科幻宣传风格模板，生成具有以下特点的提示词：
- 未来科幻风格
- 青蓝-品红霓虹色调
- 高科技质感
- 商业广告效果

## 技术实现

### 函数签名更新
```python
async def get_ai_prompt_from_subtitles(
    subtitles: List[Dict], 
    api_key: str = None, 
    custom_prompt: str = None
) -> Optional[List[Dict]]
```

### 参数说明
- `subtitles`: 字幕数据列表
- `api_key`: API密钥（可选）
- `custom_prompt`: 自定义提示词模板（可选，为None时使用默认模板）

## 故障排除

### 常见问题

1. **生成失败**: 检查自定义提示词是否包含了必要的JSON格式要求
2. **格式错误**: 确保提示词中明确要求输出JSON数组格式
3. **风格不符**: 在提示词中更详细地描述期望的视觉风格

### 调试建议

1. 先使用默认模板测试功能是否正常
2. 逐步修改自定义模板，观察输出变化
3. 参考提供的示例模板进行调整

## 更新日志

- **v1.0**: 添加自定义提示词功能
- 支持在创建项目时选择使用自定义或默认提示词模板
- 同时支持文本转语音和音频上传两种模式
- 提供友好的UI界面和使用提示

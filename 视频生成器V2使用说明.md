# 视频生成器 V2 使用说明

## 新版本特点

V2版本完全解决了字幕依赖问题，并优化了图片显示逻辑：
- ✅ **不需要ImageMagick** - 使用PIL直接生成字幕
- ✅ **字幕效果专业** - 黄色字体，黑色描边，底部居中显示
- ✅ **自动字体适配** - 根据文字长度自动调整字体大小
- ✅ **多行字幕支持** - 长文本自动换行
- ✅ **中文字体自动查找** - 自动在系统中寻找合适的中文字体
- ✅ **智能图片循环** - 图片数量少时自动循环，确保覆盖整个视频时长
- ✅ **无黑屏保证** - 视频全程都有图片显示，不会出现黑屏
- ✅ **视频封面优化** - 第一帧不再是黑屏，正确显示封面
- ✅ **优化字幕显示** - 自动去除字幕末尾标点符号

## 安装要求

### 1. Python依赖

```bash
pip install moviepy Pillow numpy python-dotenv requests aiohttp
```

### 2. FFmpeg（必需）

**macOS:**
```bash
brew install ffmpeg
```

**Windows:**
从 https://ffmpeg.org/download.html 下载并安装

**Linux:**
```bash
sudo apt-get install ffmpeg  # Ubuntu/Debian
```

### 3. 配置API凭证

在项目根目录创建或编辑`.env`文件：

```
VOLCANO_APP_ID=你的APP_ID
VOLCANO_ACCESS_TOKEN=你的ACCESS_TOKEN
```

## 渲染速度优化

### 三种渲染模式

1. **快速模式**（推荐日常使用）
   ```bash
   ./run_fast.sh
   # 或
   RENDER_QUALITY=fast python3 generate_video_from_images_v2.py
   ```
   - ultrafast预设
   - 24fps帧率
   - 2000k比特率
   - **速度提升约3-5倍**

2. **平衡模式**（默认）
   ```bash
   ./run_balanced.sh
   # 或
   RENDER_QUALITY=balanced python3 generate_video_from_images_v2.py
   ```
   - faster预设
   - 30fps帧率
   - 3000k比特率
   - **速度和质量平衡**

3. **高质量模式**
   ```bash
   ./run_high_quality.sh
   # 或
   RENDER_QUALITY=high python3 generate_video_from_images_v2.py
   ```
   - medium预设
   - 30fps帧率
   - 5000k比特率
   - **最高质量，速度较慢**

### 渲染速度对比

以1分钟视频为例（仅供参考）：
- 快速模式：约1-2分钟
- 平衡模式：约3-5分钟
- 高质量模式：约5-10分钟

## 使用步骤

### 1. 测试环境

首先运行测试脚本确保环境配置正确：

```bash
python3 test_v2.py
```

确保所有测试都通过。

### 2. 准备图片

将要使用的图片放入"矩阵产品"文件夹：

```bash
mkdir 矩阵产品
# 将图片文件复制到该文件夹
```

支持的图片格式：JPG、JPEG、PNG、BMP、GIF

### 3. 运行程序

```bash
python3 generate_video_from_images_v2.py
```

### 4. 输入文案

程序运行后会提示输入配音文案：
- 输入文案内容（支持多行）
- 输入完成后连续按两次回车

### 5. 等待处理

程序会自动执行以下步骤：
1. 调用火山引擎TTS生成语音
2. 获取字幕时间轴信息
3. 处理图片（自动缩放到1920x1080）
4. 创建字幕图像（黄色文字，黑色描边）
5. 合成视频（包含转场效果）
6. 输出最终视频文件

## 输出文件

- `output_video.mp4` - 带字幕的视频文件
- `output_video_cover.jpg` - 视频封面图（使用第一张图片）
- `output_video.srt` - SRT格式字幕文件（备用）

## 字幕效果

### 视觉样式
- **颜色**：黄色文字（RGB: 255, 255, 0）
- **描边**：黑色描边，3像素宽度
- **位置**：视频底部，距离底边10%高度
- **大小**：自动适应（40-80像素之间）
- **宽度**：最大为视频宽度的80%

### 智能字幕分割
- **自动分割长字幕**：超过30个字符或3秒的字幕会自动分割
- **按标点分割**：优先在标点符号处分割（。！？，；等）
- **去除末尾标点**：所有字幕末尾的标点符号会被自动去除
- **时间分配**：根据文字长度按比例分配显示时间
- **流畅切换**：字幕每2-3秒更换一次，阅读体验更好

例如：
- 原始："这是一个非常长的句子，包含了很多内容，需要被分割成多个短字幕。"
- 分割后：
  - "这是一个非常长的句子"（2.7秒）
  - "包含了很多内容"（2.0秒）
  - "需要被分割成多个短字幕"（2.9秒）

## 自定义选项

### 1. 修改音色

在 `main()` 函数中修改 `voice_type` 参数：

```python
voice_type = "BV701_streaming"  # 专业女声-温暖（默认）
```

可选音色：
- `BV001_streaming` - 通用女声
- `BV003_streaming` - 通用男声
- `BV703_streaming` - 专业男声-沉稳
- `BV705_streaming` - 儿童音-男孩
- `BV706_streaming` - 儿童音-女孩

### 2. 修改输出文件名

```python
output_video = "my_video.mp4"  # 自定义文件名
```

### 3. 调整转场效果

在调用 `create_video_with_subtitles` 时修改：

```python
transition_duration=1.0  # 转场时长（秒）
```

### 4. 指定每张图片显示时长

```python
image_duration=3.0  # 每张图片显示3秒
```

### 5. 调整字幕分割参数

在 `generate_speech_and_subtitles` 方法中修改：

```python
split_subs = self.split_long_subtitle(
    subtitle, 
    max_duration=3.0,  # 每条字幕最长显示3秒
    max_chars=30       # 每条字幕最多30个字符
)
```

## 图片循环说明

程序会智能处理图片显示：

1. **自动循环**：当图片数量较少时，程序会自动循环使用图片
2. **时间计算**：精确计算每张图片的显示时长，确保覆盖整个音频
3. **无缝衔接**：使用淡入淡出效果，图片切换自然流畅

例如：
- 3张图片，20秒音频 → 图片会循环显示约6-7次
- 10张图片，30秒音频 → 每张图片显示约3秒
- 确保整个视频时长内始终有图片显示

## 常见问题

### 1. 找不到中文字体？

程序会自动在以下位置查找中文字体：
- **macOS**: `/System/Library/Fonts/` （苹方、华文黑体等）
- **Windows**: `C:/Windows/Fonts/` （微软雅黑、黑体等）
- **Linux**: `/usr/share/fonts/` （文泉驿等）

如果仍有问题，可以手动指定字体路径。

### 2. 字幕显示不正常？

- 确保文本编码为UTF-8
- 检查是否成功找到中文字体
- 查看控制台输出的字体加载信息

### 3. 内存不足？

- 减少图片数量或降低图片分辨率
- 关闭其他程序释放内存
- 考虑分批处理

### 4. 渲染速度慢？

**推荐使用快速模式（速度提升3-5倍）：**
```bash
./run_fast.sh
# 或
RENDER_QUALITY=fast python3 generate_video_from_images_v2.py
```

**其他优化建议：**
- 使用快速模式可大幅提升速度
- 降低输出分辨率（如1280x720）
- 确保使用所有CPU核心
- 使用SSD硬盘提升IO性能
- 关闭其他占用CPU的程序

## 进阶使用

### 批量处理多个文件夹

```python
folders = ["产品1", "产品2", "产品3"]
for folder in folders:
    # 修改image_folder变量
    # 处理每个文件夹...
```

### 自定义字幕样式

在 `create_subtitle_image` 方法中修改：

```python
fill=(255, 255, 0, 255)  # 修改字幕颜色（R,G,B,A）
font_size = 60  # 修改字体大小
```

## 技术细节

- **字幕生成**：使用PIL的ImageDraw直接绘制文字，不依赖外部程序
- **描边效果**：通过多次偏移绘制实现粗描边效果
- **自动换行**：根据字符宽度智能换行，支持中英文混合
- **字体适配**：自动计算最佳字体大小，确保文字不会超出画面

## 测试功能

### 测试图片循环

使用专门的测试脚本验证图片循环功能：

```bash
python3 test_image_loop.py
```

这个脚本会：
- 创建3张带编号的彩色测试图片
- 生成较长的测试音频
- 验证图片是否正确循环显示

## 更新日志

### V2.4
- 修复视频第一帧黑屏问题
- 添加0.1秒静态封面帧确保缩略图正确显示
- 第一张图片不添加淡入效果
- 使用-movflags +faststart优化视频元数据
- 去除所有字幕末尾的标点符号
- 优化字幕显示效果

### V2.3
- 添加三种渲染模式（快速/平衡/高质量）
- 自动检测并使用所有CPU核心
- 快速模式速度提升3-5倍
- 添加便捷启动脚本

### V2.2
- 添加智能字幕分割功能
- 字幕按标点符号自动分割
- 限制每条字幕最长3秒显示时间
- 优化字幕阅读体验

### V2.1
- 修复图片显示时长计算问题
- 添加智能图片循环功能
- 确保视频全程无黑屏
- 优化时间计算算法

### V2.0
- 完全重写字幕生成逻辑
- 移除ImageMagick依赖
- 使用PIL直接生成字幕图像
- 改进字体查找算法
- 优化内存使用

### V1.0
- 初始版本
- 依赖moviepy的TextClip（需要ImageMagick）
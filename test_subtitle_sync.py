#!/usr/bin/env python3
"""
测试字幕同步改进效果
比较原版和改进版的字幕时间戳生成
"""

import os
from gpt_tts_api import GPTTextToSpeechAPI
from gpt_tts_api_improved import ImprovedGPTTextToSpeechAPI

def test_subtitle_generation():
    """测试字幕生成的对比"""
    
    # 测试文本
    test_texts = [
        {
            "name": "中文短句",
            "text": "这是第一句话。这是第二句话。这是第三句话。",
            "voice": "alloy"
        },
        {
            "name": "中文长句",
            "text": "人工智能技术正在快速发展，它已经深入到我们生活的方方面面。从智能手机到自动驾驶汽车，从医疗诊断到金融分析，AI无处不在。",
            "voice": "alloy"
        },
        {
            "name": "英文句子",
            "text": "Artificial intelligence is transforming our world. Machine learning models are becoming more sophisticated. The future of technology is exciting.",
            "voice": "nova"
        }
    ]
    
    # API密钥
    api_key = os.getenv('GPT_API_KEY', 'sk-kmhk2OUYmq5SskSR2e2f1d9cCb5c4e9dB7A3118116548fDe')
    
    # 初始化两个API
    old_api = GPTTextToSpeechAPI(api_key)
    new_api = ImprovedGPTTextToSpeechAPI(api_key)
    
    for test_case in test_texts:
        print(f"\n{'='*80}")
        print(f"测试案例: {test_case['name']}")
        print(f"文本: {test_case['text']}")
        print(f"字符数: {len(test_case['text'])}")
        print(f"{'='*80}")
        
        # 使用原版API（仅生成字幕，不生成音频）
        print("\n### 原版API估算 ###")
        sentences = old_api._split_text_to_sentences(test_case['text'])
        
        # 模拟原版的时间计算逻辑
        is_chinese = any('\u4e00' <= char <= '\u9fff' for char in test_case['text'])
        char_duration = 0.25 if is_chinese else 0.06
        
        current_time = 0
        old_subtitles = []
        
        for i, sentence in enumerate(sentences):
            sentence = sentence.strip()
            if sentence:
                duration = len(sentence) * char_duration * 1000
                old_subtitles.append({
                    "index": i + 1,
                    "content": sentence,
                    "start": int(current_time),
                    "end": int(current_time + duration)
                })
                current_time += duration + 200  # 200ms停顿
        
        print(f"生成字幕数: {len(old_subtitles)}")
        print(f"预估总时长: {current_time/1000:.2f} 秒")
        print("\n字幕时间轴:")
        for sub in old_subtitles:
            start = sub['start'] / 1000
            end = sub['end'] / 1000
            print(f"  [{start:6.2f}s - {end:6.2f}s] {sub['content']}")
        
        # 使用改进版API（仅生成字幕，不生成音频）
        print("\n### 改进版API估算 ###")
        sentences_new = new_api._split_text_to_sentences(test_case['text'])
        
        # 模拟改进版的时间计算逻辑
        total_chars = sum(len(s.strip()) for s in sentences_new if s.strip())
        
        # 改进的估算
        if is_chinese:
            chars_per_minute = 200  # 中文每分钟200字
        else:
            chars_per_minute = 800  # 英文每分钟800字符
        
        # 获取速度因子
        speed_factor = new_api._get_speed_factor("tts-1", test_case['voice'])
        chars_per_minute *= speed_factor
        
        # 计算总时长
        total_duration_ms = (total_chars / chars_per_minute * 60 * 1000)
        pause_time = len(sentences_new) * 100
        total_duration_ms += pause_time
        
        # 生成字幕
        start_buffer = 100
        end_buffer = 200
        available_duration = total_duration_ms - start_buffer - end_buffer
        current_time = start_buffer
        
        new_subtitles = []
        sentence_weights = [(s.strip(), len(s.strip())) for s in sentences_new if s.strip()]
        
        for i, (sentence, char_count) in enumerate(sentence_weights):
            weight = char_count / total_chars if total_chars > 0 else 1 / len(sentence_weights)
            duration = available_duration * weight
            duration = max(duration, 500)  # 最小500ms
            
            if current_time + duration > total_duration_ms - end_buffer:
                duration = total_duration_ms - end_buffer - current_time
            
            new_subtitles.append({
                "index": i + 1,
                "content": sentence,
                "start": int(current_time),
                "end": int(current_time + duration)
            })
            
            gap = 50 if i < len(sentence_weights) - 1 else 0
            current_time += duration + gap
        
        print(f"生成字幕数: {len(new_subtitles)}")
        print(f"预估总时长: {total_duration_ms/1000:.2f} 秒")
        print("\n字幕时间轴:")
        for sub in new_subtitles:
            start = sub['start'] / 1000
            end = sub['end'] / 1000
            print(f"  [{start:6.2f}s - {end:6.2f}s] {sub['content']}")
        
        # 对比分析
        print("\n### 对比分析 ###")
        old_total = sum(sub['end'] - sub['start'] for sub in old_subtitles) / 1000
        new_total = sum(sub['end'] - sub['start'] for sub in new_subtitles) / 1000
        
        print(f"原版总字幕显示时间: {old_total:.2f} 秒")
        print(f"改进版总字幕显示时间: {new_total:.2f} 秒")
        print(f"时间差异: {abs(old_total - new_total):.2f} 秒")
        
        # 分析句子间隔
        old_gaps = []
        for i in range(len(old_subtitles) - 1):
            gap = old_subtitles[i+1]['start'] - old_subtitles[i]['end']
            old_gaps.append(gap)
        
        new_gaps = []
        for i in range(len(new_subtitles) - 1):
            gap = new_subtitles[i+1]['start'] - new_subtitles[i]['end']
            new_gaps.append(gap)
        
        if old_gaps:
            print(f"\n原版句子间隔: {[f'{g}ms' for g in old_gaps]}")
            print(f"平均间隔: {sum(old_gaps)/len(old_gaps):.0f}ms")
        
        if new_gaps:
            print(f"\n改进版句子间隔: {[f'{g}ms' for g in new_gaps]}")
            print(f"平均间隔: {sum(new_gaps)/len(new_gaps):.0f}ms")

def main():
    """主函数"""
    print("字幕同步改进测试")
    print("="*80)
    print("此测试比较原版和改进版的字幕时间戳生成逻辑")
    print("注意：这是基于估算的测试，实际音频时长可能有所不同")
    
    test_subtitle_generation()
    
    print("\n\n测试完成！")
    print("\n主要改进点：")
    print("1. 更准确的语速估算（基于实际语速数据）")
    print("2. 考虑不同语音和模型的速度差异")
    print("3. 减少句子间的固定停顿，使用动态间隔")
    print("4. 添加开始和结束缓冲，避免字幕过早或过晚")
    print("5. 如果可能，使用ffprobe获取实际音频时长")

if __name__ == "__main__":
    main()
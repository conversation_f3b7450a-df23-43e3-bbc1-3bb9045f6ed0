#!/usr/bin/env python3
"""
修复数据存储格式
重新设计时间数据的存储方式，确保剪映导出正常工作
"""

import sys
import os
from database import Database

def fix_project_data_storage(project_id: int):
    """修复项目的数据存储格式"""
    db = Database()
    
    print(f"🔧 修复项目 {project_id} 的数据存储格式")
    print("=" * 60)
    
    # 获取项目详情
    project = db.get_project_details(project_id)
    if not project:
        print(f"❌ 项目 {project_id} 不存在")
        return False
    
    print(f"📋 项目名称: {project['name']}")
    
    # 1. 检查并修复音频数据
    print(f"\n🎵 检查音频数据...")
    if not project['audio_files']:
        print("❌ 没有音频文件")
        return False
    
    audio_info = project['audio_files'][0]
    audio_path = audio_info['file_path']
    
    if not os.path.exists(audio_path):
        print(f"❌ 音频文件不存在: {audio_path}")
        return False
    
    # 获取真实的音频时长
    try:
        import pyJianYingDraft as draft
        audio_material = draft.Audio_material(audio_path)
        real_audio_duration_us = audio_material.duration
        real_audio_duration_sec = real_audio_duration_us / 1000000
        
        print(f"✅ 音频文件: {audio_path}")
        print(f"✅ 真实音频时长: {real_audio_duration_sec:.2f}s ({real_audio_duration_us} 微秒)")
        
        # 更新数据库中的音频时长
        db.conn.execute(
            "UPDATE audio_files SET duration = ? WHERE project_id = ?",
            (real_audio_duration_sec, project_id)
        )
        
    except Exception as e:
        print(f"❌ 获取音频时长失败: {str(e)}")
        return False
    
    # 2. 检查并修复图片数据
    print(f"\n🖼️  检查图片数据...")
    if not project['images']:
        print("❌ 没有图片文件")
        return False
    
    success_images = [img for img in project['images'] if img['status'] == 'success']
    if not success_images:
        print("❌ 没有成功的图片")
        return False
    
    print(f"📸 找到 {len(success_images)} 张成功的图片")
    
    # 验证图片文件存在
    valid_images = []
    for img in success_images:
        if os.path.exists(img['file_path']):
            valid_images.append(img)
        else:
            print(f"⚠️  图片文件不存在: {img['file_path']}")
    
    if not valid_images:
        print("❌ 没有有效的图片文件")
        return False
    
    print(f"✅ 有效图片: {len(valid_images)} 张")
    
    # 3. 重新计算并存储正确的时间数据
    print(f"\n⏰ 重新计算时间分配...")
    
    # 按场景索引排序
    valid_images.sort(key=lambda x: x['scene_index'])
    
    # 平均分配时间
    image_duration_sec = real_audio_duration_sec / len(valid_images)
    
    print(f"   音频总时长: {real_audio_duration_sec:.2f}s")
    print(f"   图片数量: {len(valid_images)} 张")
    print(f"   每张图片时长: {image_duration_sec:.2f}s")
    
    # 更新每张图片的时间
    for i, img in enumerate(valid_images):
        start_time_sec = i * image_duration_sec
        end_time_sec = (i + 1) * image_duration_sec
        
        # 确保最后一张图片不超出音频时长
        if end_time_sec > real_audio_duration_sec:
            end_time_sec = real_audio_duration_sec
        
        duration_sec = end_time_sec - start_time_sec
        
        if duration_sec <= 0:
            print(f"⚠️  图片 {i+1} 持续时间无效，跳过")
            continue
        
        print(f"   图片 {i+1} (场景{img['scene_index']}): {start_time_sec:.2f}s - {end_time_sec:.2f}s")
        
        # 更新数据库
        try:
            db.conn.execute(
                "UPDATE images SET start_time = ?, end_time = ? WHERE id = ?",
                (start_time_sec, end_time_sec, img['id'])
            )
        except Exception as e:
            print(f"❌ 更新图片 {i+1} 时间失败: {str(e)}")
            return False
    
    # 4. 同步更新AI提示词时间（如果存在）
    print(f"\n🤖 同步AI提示词时间...")
    if project['ai_prompts']:
        ai_prompts = project['ai_prompts']
        ai_prompts.sort(key=lambda x: x['scene_index'])
        
        for i, prompt in enumerate(ai_prompts):
            if i < len(valid_images):
                start_time_sec = i * image_duration_sec
                end_time_sec = (i + 1) * image_duration_sec
                
                if end_time_sec > real_audio_duration_sec:
                    end_time_sec = real_audio_duration_sec
                
                print(f"   AI提示词 {i+1} (场景{prompt['scene_index']}): {start_time_sec:.2f}s - {end_time_sec:.2f}s")
                
                try:
                    db.conn.execute(
                        "UPDATE ai_prompts SET start_time = ?, end_time = ? WHERE id = ?",
                        (start_time_sec, end_time_sec, prompt['id'])
                    )
                except Exception as e:
                    print(f"❌ 更新AI提示词 {i+1} 时间失败: {str(e)}")
    
    # 5. 提交所有更改
    try:
        db.conn.commit()
        print(f"\n🎉 数据存储格式修复完成！")
        print(f"✅ 音频时长已更新为真实值")
        print(f"✅ 图片时间已重新分配")
        print(f"✅ AI提示词时间已同步")
        print(f"\n💡 现在可以尝试重新创建剪映草稿")
        return True
        
    except Exception as e:
        print(f"❌ 提交更改失败: {str(e)}")
        db.conn.rollback()
        return False

def create_optimized_jianying_export(project_id: int, jianying_drafts_path: str, draft_name: str = None):
    """创建优化的剪映导出"""
    print(f"\n🚀 创建优化的剪映导出...")
    
    try:
        import pyJianYingDraft as draft
        from datetime import datetime
        
        db = Database()
        
        # 获取修复后的项目数据
        project = db.get_project_details(project_id)
        if not project:
            print(f"❌ 项目不存在")
            return False
        
        if not draft_name:
            draft_name = f"{project['name']}_fixed_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        print(f"📝 草稿名称: {draft_name}")
        
        # 创建Script对象
        script = draft.Script_file(1920, 1080)
        
        # 1. 添加音频轨道
        print(f"🎵 添加音频轨道...")
        script.add_track(draft.Track_type.audio, "主音频")
        
        audio_info = project['audio_files'][0]
        audio_path = audio_info['file_path']
        audio_material = draft.Audio_material(audio_path)
        audio_duration = audio_material.duration
        
        audio_segment = draft.Audio_segment(audio_material, draft.trange(0, audio_duration))
        script.add_segment(audio_segment, "主音频")
        
        print(f"✅ 音频时长: {audio_duration / 1000000:.2f}s")
        
        # 2. 添加视频轨道（图片）
        print(f"🖼️  添加视频轨道...")
        script.add_track(draft.Track_type.video, "分镜图片")
        
        success_images = [img for img in project['images'] if img['status'] == 'success']
        success_images.sort(key=lambda x: x['scene_index'])
        
        added_count = 0
        for i, image_info in enumerate(success_images):
            image_path = image_info['file_path']
            start_time_sec = image_info['start_time']
            end_time_sec = image_info['end_time']

            # 验证图片文件存在
            if not os.path.exists(image_path):
                print(f"   ❌ 图片 {i+1} 文件不存在: {image_path}")
                continue

            start_time_us = int(start_time_sec * 1000000)
            end_time_us = int(end_time_sec * 1000000)
            duration_us = end_time_us - start_time_us

            if duration_us <= 0:
                print(f"   ❌ 图片 {i+1} 持续时间无效: {duration_us / 1000000:.2f}s")
                continue

            print(f"   🎨 处理图片 {i+1}/{len(success_images)}: 场景{image_info['scene_index']}")
            print(f"      时间: {start_time_sec:.2f}s - {end_time_sec:.2f}s (持续 {duration_us / 1000000:.2f}s)")
            print(f"      路径: {image_path}")

            try:
                # 创建图片素材和片段
                print(f"      📁 创建图片素材...")
                image_material = draft.Video_material(image_path)
                print(f"      📊 图片素材时长: {image_material.duration / 1000000:.2f}s")

                print(f"      🎬 创建视频片段...")
                image_segment = draft.Video_segment(
                    image_material,
                    draft.trange(start_time_us, duration_us),
                    clip_settings=draft.Clip_settings(
                        scale_x=1.0,
                        scale_y=1.0,
                        transform_x=0.0,
                        transform_y=0.0
                    )
                )

                # 尝试添加动画（使用英文名称或跳过）
                try:
                    if i == 0:
                        # 尝试不同的淡入动画名称
                        if hasattr(draft.Intro_type, 'fade_in'):
                            image_segment.add_animation(draft.Intro_type.fade_in)
                            print(f"      ✨ 添加fade_in动画")
                        elif hasattr(draft.Intro_type, 'FadeIn'):
                            image_segment.add_animation(draft.Intro_type.FadeIn)
                            print(f"      ✨ 添加FadeIn动画")
                        else:
                            print(f"      ⚠️  跳过淡入动画（未找到合适的类型）")

                    if i == len(success_images) - 1:
                        # 尝试不同的淡出动画名称
                        if hasattr(draft.Outro_type, 'fade_out'):
                            image_segment.add_animation(draft.Outro_type.fade_out)
                            print(f"      ✨ 添加fade_out动画")
                        elif hasattr(draft.Outro_type, 'FadeOut'):
                            image_segment.add_animation(draft.Outro_type.FadeOut)
                            print(f"      ✨ 添加FadeOut动画")
                        else:
                            print(f"      ⚠️  跳过淡出动画（未找到合适的类型）")

                except Exception as anim_e:
                    print(f"      ⚠️  添加动画失败，跳过: {str(anim_e)}")

                print(f"      ➕ 添加到轨道...")
                script.add_segment(image_segment, "分镜图片")
                added_count += 1
                print(f"   ✅ 图片 {i+1} 添加成功")

            except Exception as e:
                print(f"   ❌ 图片 {i+1} 添加失败: {str(e)}")
                import traceback
                traceback.print_exc()
                continue

        print(f"🎉 图片添加完成: {added_count}/{len(success_images)} 张图片成功添加")
        
        # 3. 添加字幕轨道
        print(f"📝 添加字幕轨道...")
        script.add_track(draft.Track_type.text, "字幕")
        
        subtitle_info = project['subtitles'][0]
        srt_path = subtitle_info['srt_file_path']
        
        script.import_srt(
            srt_path,
            track_name="字幕",
            text_style=draft.Text_style(
                size=6.0,
                color=(1.0, 1.0, 1.0),
                bold=True,
                align=1
            ),
            clip_settings=draft.Clip_settings(
                transform_y=-0.8
            )
        )
        
        print(f"✅ 字幕导入成功")
        
        # 4. 保存草稿
        print(f"💾 保存草稿...")
        draft_dir = os.path.join(jianying_drafts_path, draft_name)
        os.makedirs(draft_dir, exist_ok=True)
        
        draft_content_path = os.path.join(draft_dir, "draft_content.json")
        script.dump(draft_content_path)
        
        # 创建meta信息
        import json
        meta_info = {
            "draft_id": str(os.urandom(16).hex()),
            "draft_name": draft_name,
            "draft_folder": draft_name,
            "create_time": int(datetime.now().timestamp() * 1000),
            "update_time": int(datetime.now().timestamp() * 1000),
            "duration": int(audio_duration / 1000),
            "cover_path": "",
            "fps": 30,
            "resolution": {"width": 1920, "height": 1080}
        }
        
        meta_info_path = os.path.join(draft_dir, "draft_meta_info.json")
        with open(meta_info_path, 'w', encoding='utf-8') as f:
            json.dump(meta_info, f, ensure_ascii=False, indent=2)
        
        # 更新数据库
        db.conn.execute(
            "UPDATE projects SET jianying_draft_path = ? WHERE id = ?",
            (draft_dir, project_id)
        )
        db.conn.commit()
        
        print(f"🎉 优化的剪映草稿创建成功！")
        print(f"📂 草稿位置: {draft_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建剪映草稿失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    if len(sys.argv) < 2:
        print("用法: python fix_data_storage.py <project_id> [jianying_drafts_path]")
        print("  如果提供jianying_drafts_path，将同时创建优化的剪映草稿")
        sys.exit(1)
    
    try:
        project_id = int(sys.argv[1])
        
        # 修复数据存储格式
        success = fix_project_data_storage(project_id)
        
        if success and len(sys.argv) > 2:
            jianying_drafts_path = sys.argv[2]
            create_optimized_jianying_export(project_id, jianying_drafts_path)
        
    except ValueError:
        print("错误: project_id 必须是数字")
        sys.exit(1)

if __name__ == "__main__":
    main()

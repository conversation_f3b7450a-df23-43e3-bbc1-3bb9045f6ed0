import sqlite3
import json
import os
from datetime import datetime
from typing import Optional, List, Dict, Any
import threading

class Database:
    def __init__(self, db_path: str = "digitalpeople.db"):
        self.db_path = db_path
        self._local = threading.local()
        self.init_database()
    
    @property
    def conn(self):
        """获取线程本地的数据库连接"""
        if not hasattr(self._local, 'conn'):
            self._local.conn = sqlite3.connect(self.db_path)
            self._local.conn.row_factory = sqlite3.Row
        return self._local.conn
    
    def init_database(self):
        """初始化数据库表结构"""
        with self.conn:
            # 创建项目表
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS projects (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    input_text TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    jianying_draft_path TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 检查并添加新字段（用于数据库迁移）
            cursor = self.conn.execute("PRAGMA table_info(projects)")
            columns = [col[1] for col in cursor.fetchall()]
            if 'jianying_draft_path' not in columns:
                self.conn.execute("ALTER TABLE projects ADD COLUMN jianying_draft_path TEXT")
                self.conn.commit()
            
            # 创建音频表
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS audio_files (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER NOT NULL,
                    file_path TEXT NOT NULL,
                    file_name TEXT NOT NULL,
                    voice_type TEXT,
                    format TEXT,
                    sample_rate INTEGER,
                    speed REAL,
                    volume REAL,
                    pitch REAL,
                    file_size INTEGER,
                    duration REAL,
                    volcano_task_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects(id)
                )
            """)
            
            # 创建字幕表
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS subtitles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER NOT NULL,
                    audio_id INTEGER,
                    srt_file_path TEXT,
                    srt_content TEXT,
                    json_content TEXT,
                    volcano_task_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects(id),
                    FOREIGN KEY (audio_id) REFERENCES audio_files(id)
                )
            """)
            
            # 创建AI提示词表
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS ai_prompts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER NOT NULL,
                    subtitle_id INTEGER,
                    scene_index INTEGER,
                    start_time REAL,
                    end_time REAL,
                    prompt_text TEXT,
                    json_file_path TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects(id),
                    FOREIGN KEY (subtitle_id) REFERENCES subtitles(id)
                )
            """)
            
            # 创建图片表
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS images (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER NOT NULL,
                    prompt_id INTEGER,
                    scene_index INTEGER,
                    start_time REAL,
                    end_time REAL,
                    file_path TEXT NOT NULL,
                    file_name TEXT NOT NULL,
                    image_url TEXT,
                    prompt_used TEXT,
                    model_used TEXT,
                    status TEXT DEFAULT 'success',
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects(id),
                    FOREIGN KEY (prompt_id) REFERENCES ai_prompts(id)
                )
            """)
            
            # 创建进度跟踪表
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS project_progress (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER NOT NULL,
                    step_name TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    progress INTEGER DEFAULT 0,
                    message TEXT,
                    started_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects(id)
                )
            """)
    
    def create_project(self, name: str, input_text: str) -> int:
        """创建新项目"""
        cursor = self.conn.execute(
            "INSERT INTO projects (name, input_text) VALUES (?, ?)",
            (name, input_text)
        )
        self.conn.commit()
        project_id = cursor.lastrowid
        
        # 初始化项目进度步骤
        steps = [
            ('text_to_speech', '文本转语音'),
            ('generate_subtitle', '生成字幕'),
            ('generate_ai_prompt', '生成AI提示词'),
            ('generate_images', '生成分镜图片')
        ]
        
        for step_name, message in steps:
            self.conn.execute(
                """INSERT INTO project_progress 
                   (project_id, step_name, message) 
                   VALUES (?, ?, ?)""",
                (project_id, step_name, message)
            )
        self.conn.commit()
        
        return project_id
    
    def update_project_status(self, project_id: int, status: str):
        """更新项目状态"""
        self.conn.execute(
            """UPDATE projects 
               SET status = ?, updated_at = CURRENT_TIMESTAMP 
               WHERE id = ?""",
            (status, project_id)
        )
        self.conn.commit()
    
    def save_audio_file(self, project_id: int, file_path: str, file_name: str,
                       voice_type: str = None, format: str = None,
                       sample_rate: int = None, speed: float = None,
                       volume: float = None, pitch: float = None,
                       file_size: int = None, duration: float = None,
                       volcano_task_id: str = None) -> int:
        """保存音频文件信息"""
        cursor = self.conn.execute(
            """INSERT INTO audio_files 
               (project_id, file_path, file_name, voice_type, format, 
                sample_rate, speed, volume, pitch, file_size, duration, volcano_task_id)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
            (project_id, file_path, file_name, voice_type, format,
             sample_rate, speed, volume, pitch, file_size, duration, volcano_task_id)
        )
        self.conn.commit()
        return cursor.lastrowid
    
    def save_subtitle(self, project_id: int, audio_id: int = None,
                     srt_file_path: str = None, srt_content: str = None,
                     json_content: str = None, volcano_task_id: str = None) -> int:
        """保存字幕信息"""
        cursor = self.conn.execute(
            """INSERT INTO subtitles 
               (project_id, audio_id, srt_file_path, srt_content, json_content, volcano_task_id)
               VALUES (?, ?, ?, ?, ?, ?)""",
            (project_id, audio_id, srt_file_path, srt_content, json_content, volcano_task_id)
        )
        self.conn.commit()
        return cursor.lastrowid
    
    def save_ai_prompts(self, project_id: int, subtitle_id: int,
                       prompts: List[Dict], json_file_path: str = None):
        """保存AI提示词"""
        for prompt in prompts:
            self.conn.execute(
                """INSERT INTO ai_prompts 
                   (project_id, subtitle_id, scene_index, start_time, end_time, 
                    prompt_text, json_file_path)
                   VALUES (?, ?, ?, ?, ?, ?, ?)""",
                (project_id, subtitle_id, prompt.get('scene_index', 0),
                 prompt['start'] / 1000.0, prompt['end'] / 1000.0,
                 prompt['img_prompt'], json_file_path)
            )
        self.conn.commit()
    
    def save_image(self, project_id: int, prompt_id: int = None,
                   scene_index: int = None, start_time: float = None,
                   end_time: float = None, file_path: str = None,
                   file_name: str = None, image_url: str = None,
                   prompt_used: str = None, model_used: str = None,
                   status: str = 'success', error_message: str = None) -> int:
        """保存图片信息"""
        cursor = self.conn.execute(
            """INSERT INTO images 
               (project_id, prompt_id, scene_index, start_time, end_time,
                file_path, file_name, image_url, prompt_used, model_used,
                status, error_message)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
            (project_id, prompt_id, scene_index, start_time, end_time,
             file_path, file_name, image_url, prompt_used, model_used,
             status, error_message)
        )
        self.conn.commit()
        return cursor.lastrowid
    
    def update_progress(self, project_id: int, step_name: str,
                       status: str = None, progress: int = None,
                       message: str = None):
        """更新项目进度"""
        updates = []
        params = []
        
        if status is not None:
            updates.append("status = ?")
            params.append(status)
            
            if status == 'in_progress' and 'started_at' not in updates:
                updates.append("started_at = CURRENT_TIMESTAMP")
            elif status == 'completed' and 'completed_at' not in updates:
                updates.append("completed_at = CURRENT_TIMESTAMP")
        
        if progress is not None:
            updates.append("progress = ?")
            params.append(progress)
        
        if message is not None:
            updates.append("message = ?")
            params.append(message)
        
        if updates:
            params.extend([project_id, step_name])
            query = f"""UPDATE project_progress 
                       SET {', '.join(updates)}
                       WHERE project_id = ? AND step_name = ?"""
            self.conn.execute(query, params)
            self.conn.commit()
    
    def get_project_progress(self, project_id: int) -> List[Dict]:
        """获取项目进度"""
        cursor = self.conn.execute(
            """SELECT step_name, status, progress, message, started_at, completed_at
               FROM project_progress
               WHERE project_id = ?
               ORDER BY id""",
            (project_id,)
        )
        return [dict(row) for row in cursor.fetchall()]
    
    def get_project_details(self, project_id: int) -> Dict:
        """获取项目详细信息"""
        # 获取项目基本信息
        project = dict(self.conn.execute(
            "SELECT * FROM projects WHERE id = ?", (project_id,)
        ).fetchone())
        
        # 获取音频文件
        project['audio_files'] = [dict(row) for row in self.conn.execute(
            "SELECT * FROM audio_files WHERE project_id = ?", (project_id,)
        ).fetchall()]
        
        # 获取字幕
        project['subtitles'] = [dict(row) for row in self.conn.execute(
            "SELECT * FROM subtitles WHERE project_id = ?", (project_id,)
        ).fetchall()]
        
        # 获取AI提示词
        project['ai_prompts'] = [dict(row) for row in self.conn.execute(
            "SELECT * FROM ai_prompts WHERE project_id = ? ORDER BY scene_index", 
            (project_id,)
        ).fetchall()]
        
        # 获取图片
        project['images'] = [dict(row) for row in self.conn.execute(
            "SELECT * FROM images WHERE project_id = ? ORDER BY scene_index", 
            (project_id,)
        ).fetchall()]
        
        # 获取进度
        project['progress'] = self.get_project_progress(project_id)
        
        return project
    
    def get_all_projects(self) -> List[Dict]:
        """获取所有项目列表"""
        cursor = self.conn.execute(
            """SELECT id, name, status, created_at, updated_at 
               FROM projects 
               ORDER BY created_at DESC"""
        )
        return [dict(row) for row in cursor.fetchall()]
    
    def close(self):
        """关闭数据库连接"""
        if hasattr(self._local, 'conn'):
            self._local.conn.close()
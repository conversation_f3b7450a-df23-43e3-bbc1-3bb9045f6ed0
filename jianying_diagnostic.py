import os
import json
from typing import Dict, List
from PIL import Image
from database import Database

class JianyingDiagnostic:
    """
    剪映草稿诊断工具
    用于检查和诊断图片不显示的问题
    """
    
    def __init__(self):
        self.db = Database()
    
    def diagnose_project(self, project_id: int) -> Dict:
        """
        诊断项目的剪映导出问题
        """
        print(f"🔍 开始诊断项目 {project_id}...")
        
        # 获取项目详情
        project = self.db.get_project_details(project_id)
        if not project:
            return {"error": f"项目 {project_id} 不存在"}
        
        diagnosis = {
            "project_id": project_id,
            "project_name": project['name'],
            "issues": [],
            "warnings": [],
            "recommendations": [],
            "file_analysis": {}
        }
        
        # 检查音频文件
        self._check_audio_files(project, diagnosis)
        
        # 检查字幕文件
        self._check_subtitle_files(project, diagnosis)
        
        # 检查图片文件
        self._check_image_files(project, diagnosis)
        
        # 检查已生成的草稿
        self._check_existing_draft(project, diagnosis)
        
        # 生成建议
        self._generate_recommendations(diagnosis)
        
        return diagnosis
    
    def _check_audio_files(self, project: Dict, diagnosis: Dict):
        """检查音频文件"""
        print("🎵 检查音频文件...")
        
        audio_files = project.get('audio_files', [])
        if not audio_files:
            diagnosis['issues'].append("没有音频文件")
            return
        
        audio_info = audio_files[0]
        audio_path = audio_info['file_path']
        
        if not os.path.exists(audio_path):
            diagnosis['issues'].append(f"音频文件不存在: {audio_path}")
            return
        
        file_size = os.path.getsize(audio_path)
        diagnosis['file_analysis']['audio'] = {
            "path": audio_path,
            "exists": True,
            "size": file_size,
            "duration": audio_info.get('duration'),
            "format": audio_info.get('format')
        }
        
        if file_size == 0:
            diagnosis['issues'].append("音频文件为空")
        elif file_size < 1024:  # 小于1KB
            diagnosis['warnings'].append("音频文件过小，可能损坏")
        
        print(f"✅ 音频文件检查完成: {file_size} bytes")
    
    def _check_subtitle_files(self, project: Dict, diagnosis: Dict):
        """检查字幕文件"""
        print("📝 检查字幕文件...")
        
        subtitles = project.get('subtitles', [])
        if not subtitles:
            diagnosis['issues'].append("没有字幕文件")
            return
        
        subtitle_info = subtitles[0]
        srt_path = subtitle_info['srt_file_path']
        
        if not os.path.exists(srt_path):
            diagnosis['issues'].append(f"字幕文件不存在: {srt_path}")
            return
        
        # 分析SRT文件内容
        try:
            with open(srt_path, 'r', encoding='utf-8') as f:
                srt_content = f.read()
            
            # 简单统计字幕条数
            subtitle_count = srt_content.count('\n\n') + 1
            
            diagnosis['file_analysis']['subtitle'] = {
                "path": srt_path,
                "exists": True,
                "size": len(srt_content),
                "subtitle_count": subtitle_count,
                "encoding": "utf-8"
            }
            
            if len(srt_content) == 0:
                diagnosis['issues'].append("字幕文件为空")
            elif subtitle_count < 2:
                diagnosis['warnings'].append("字幕条数过少")
            
            print(f"✅ 字幕文件检查完成: {subtitle_count} 条字幕")
            
        except Exception as e:
            diagnosis['issues'].append(f"字幕文件读取失败: {str(e)}")
    
    def _check_image_files(self, project: Dict, diagnosis: Dict):
        """检查图片文件"""
        print("🖼️  检查图片文件...")
        
        images = project.get('images', [])
        if not images:
            diagnosis['issues'].append("没有图片文件")
            return
        
        success_images = [img for img in images if img['status'] == 'success']
        failed_images = [img for img in images if img['status'] != 'success']
        
        diagnosis['file_analysis']['images'] = {
            "total_count": len(images),
            "success_count": len(success_images),
            "failed_count": len(failed_images),
            "details": []
        }
        
        if not success_images:
            diagnosis['issues'].append("没有成功生成的图片")
            return
        
        # 检查每张图片
        for i, img_info in enumerate(success_images):
            img_path = img_info['file_path']
            start_time = img_info['start_time']
            end_time = img_info['end_time']
            duration = end_time - start_time

            img_detail = {
                "index": i + 1,
                "scene_index": img_info['scene_index'],
                "path": img_path,
                "start_time": start_time,
                "end_time": end_time,
                "duration": duration,
                "exists": os.path.exists(img_path)
            }
            
            if not img_detail['exists']:
                diagnosis['issues'].append(f"图片文件不存在: {img_path}")
                img_detail['issue'] = "文件不存在"
            else:
                # 分析图片属性
                try:
                    file_size = os.path.getsize(img_path)
                    img_detail['file_size'] = file_size
                    
                    with Image.open(img_path) as img:
                        img_detail['width'] = img.width
                        img_detail['height'] = img.height
                        img_detail['format'] = img.format
                        img_detail['mode'] = img.mode
                    
                    # 检查图片问题
                    if file_size == 0:
                        diagnosis['issues'].append(f"图片文件为空: {img_path}")
                        img_detail['issue'] = "文件为空"
                    elif file_size < 1024:  # 小于1KB
                        diagnosis['warnings'].append(f"图片文件过小: {img_path}")
                        img_detail['warning'] = "文件过小"
                    
                    # 检查图片格式
                    if img_detail['format'] not in ['PNG', 'JPEG', 'JPG']:
                        diagnosis['warnings'].append(f"图片格式可能不兼容: {img_detail['format']}")
                        img_detail['warning'] = f"格式: {img_detail['format']}"
                    
                    # 检查图片尺寸
                    if img_detail['width'] < 100 or img_detail['height'] < 100:
                        diagnosis['warnings'].append(f"图片尺寸过小: {img_detail['width']}x{img_detail['height']}")
                        img_detail['warning'] = "尺寸过小"
                    
                except Exception as e:
                    diagnosis['issues'].append(f"图片分析失败: {img_path} - {str(e)}")
                    img_detail['issue'] = f"分析失败: {str(e)}"
            
            diagnosis['file_analysis']['images']['details'].append(img_detail)

        # 时间范围分析
        if success_images:
            # 获取音频时长用于对比
            audio_duration = None
            if project.get('audio_files'):
                audio_duration = project['audio_files'][0].get('duration')

            if audio_duration:
                print(f"🕐 时间范围分析:")
                print(f"   音频时长: {audio_duration:.2f}s")

                for img_detail in diagnosis['file_analysis']['images']['details']:
                    start_time = img_detail['start_time']
                    end_time = img_detail['end_time']
                    duration = img_detail['duration']

                    # 检查时间问题
                    if start_time >= audio_duration:
                        diagnosis['issues'].append(f"图片场景{img_detail['scene_index']}开始时间({start_time:.2f}s)超出音频时长({audio_duration:.2f}s)")
                        img_detail['time_issue'] = "开始时间超出"
                    elif end_time > audio_duration:
                        diagnosis['warnings'].append(f"图片场景{img_detail['scene_index']}结束时间({end_time:.2f}s)超出音频时长({audio_duration:.2f}s)")
                        img_detail['time_issue'] = "结束时间超出"
                    elif duration <= 0:
                        diagnosis['issues'].append(f"图片场景{img_detail['scene_index']}持续时间无效({duration:.2f}s)")
                        img_detail['time_issue'] = "持续时间无效"

                    print(f"   场景{img_detail['scene_index']}: {start_time:.2f}s - {end_time:.2f}s (持续{duration:.2f}s)")

        print(f"✅ 图片文件检查完成: {len(success_images)} 张成功, {len(failed_images)} 张失败")
    
    def _check_existing_draft(self, project: Dict, diagnosis: Dict):
        """检查已生成的草稿"""
        print("📂 检查已生成的草稿...")
        
        draft_path = project.get('jianying_draft_path')
        if not draft_path:
            diagnosis['warnings'].append("尚未生成剪映草稿")
            return
        
        if not os.path.exists(draft_path):
            diagnosis['issues'].append(f"草稿文件夹不存在: {draft_path}")
            return
        
        # 检查草稿文件
        draft_content_path = os.path.join(draft_path, "draft_content.json")
        meta_info_path = os.path.join(draft_path, "draft_meta_info.json")
        
        draft_analysis = {
            "path": draft_path,
            "exists": True,
            "has_content": os.path.exists(draft_content_path),
            "has_meta": os.path.exists(meta_info_path)
        }
        
        if draft_analysis['has_content']:
            try:
                with open(draft_content_path, 'r', encoding='utf-8') as f:
                    draft_data = json.load(f)
                
                # 分析草稿内容
                tracks = draft_data.get('tracks', [])
                video_tracks = [t for t in tracks if t.get('type') == 'video']
                audio_tracks = [t for t in tracks if t.get('type') == 'audio']
                text_tracks = [t for t in tracks if t.get('type') == 'text']
                
                draft_analysis.update({
                    "file_size": os.path.getsize(draft_content_path),
                    "video_tracks": len(video_tracks),
                    "audio_tracks": len(audio_tracks),
                    "text_tracks": len(text_tracks),
                    "total_segments": sum(len(t.get('segments', [])) for t in tracks)
                })
                
                # 检查视频片段
                video_segments = 0
                for track in video_tracks:
                    segments = track.get('segments', [])
                    video_segments += len(segments)
                
                if video_segments == 0:
                    diagnosis['issues'].append("草稿中没有视频片段（图片）")
                else:
                    print(f"✅ 草稿包含 {video_segments} 个视频片段")
                
            except Exception as e:
                diagnosis['issues'].append(f"草稿文件解析失败: {str(e)}")
        else:
            diagnosis['issues'].append("草稿内容文件不存在")
        
        diagnosis['file_analysis']['draft'] = draft_analysis
    
    def _generate_recommendations(self, diagnosis: Dict):
        """生成修复建议"""
        recommendations = []
        
        # 基于问题生成建议
        issues = diagnosis['issues']
        warnings = diagnosis['warnings']
        
        if any("图片文件不存在" in issue for issue in issues):
            recommendations.append("重新生成缺失的图片文件")
        
        if any("草稿中没有视频片段" in issue for issue in issues):
            recommendations.append("使用修复版导出器重新创建草稿")
        
        if any("格式可能不兼容" in warning for warning in warnings):
            recommendations.append("将图片转换为PNG格式")
        
        if any("尺寸过小" in warning for warning in warnings):
            recommendations.append("使用更高分辨率的图片")
        
        if any("文件过小" in warning for warning in warnings):
            recommendations.append("检查文件是否完整生成")
        
        # 通用建议
        recommendations.extend([
            "确保使用剪映5.9及以上版本",
            "检查图片文件路径是否包含特殊字符",
            "尝试使用修复版导出器",
            "确保剪映草稿文件夹路径正确"
        ])
        
        diagnosis['recommendations'] = recommendations
    
    def print_diagnosis_report(self, diagnosis: Dict):
        """打印诊断报告"""
        print("\n" + "="*60)
        print(f"📊 剪映草稿诊断报告")
        print("="*60)
        print(f"项目: {diagnosis['project_name']} (ID: {diagnosis['project_id']})")
        
        # 问题
        if diagnosis['issues']:
            print(f"\n❌ 发现 {len(diagnosis['issues'])} 个问题:")
            for i, issue in enumerate(diagnosis['issues'], 1):
                print(f"   {i}. {issue}")
        
        # 警告
        if diagnosis['warnings']:
            print(f"\n⚠️  发现 {len(diagnosis['warnings'])} 个警告:")
            for i, warning in enumerate(diagnosis['warnings'], 1):
                print(f"   {i}. {warning}")
        
        # 文件分析
        print(f"\n📁 文件分析:")
        file_analysis = diagnosis['file_analysis']
        
        if 'audio' in file_analysis:
            audio = file_analysis['audio']
            print(f"   音频: {'✅' if audio['exists'] else '❌'} {audio.get('size', 0)} bytes")
        
        if 'subtitle' in file_analysis:
            subtitle = file_analysis['subtitle']
            print(f"   字幕: {'✅' if subtitle['exists'] else '❌'} {subtitle.get('subtitle_count', 0)} 条")
        
        if 'images' in file_analysis:
            images = file_analysis['images']
            print(f"   图片: {images['success_count']}/{images['total_count']} 张成功")
        
        if 'draft' in file_analysis:
            draft = file_analysis['draft']
            if draft['exists']:
                print(f"   草稿: ✅ {draft.get('total_segments', 0)} 个片段")
            else:
                print(f"   草稿: ❌ 不存在")
        
        # 建议
        if diagnosis['recommendations']:
            print(f"\n💡 修复建议:")
            for i, rec in enumerate(diagnosis['recommendations'], 1):
                print(f"   {i}. {rec}")
        
        print("\n" + "="*60)

def diagnose_project_cli(project_id: int):
    """命令行诊断工具"""
    diagnostic = JianyingDiagnostic()
    diagnosis = diagnostic.diagnose_project(project_id)
    diagnostic.print_diagnosis_report(diagnosis)
    return diagnosis

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        project_id = int(sys.argv[1])
        diagnose_project_cli(project_id)
    else:
        print("用法: python jianying_diagnostic.py <project_id>")

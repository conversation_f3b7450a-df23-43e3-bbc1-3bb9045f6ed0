#!/usr/bin/env python3
"""
图片配音视频生成脚本 V5 - 火山引擎版
功能：
1. 使用GPT TTS生成高质量语音
2. 使用火山引擎ASR获取精确的字幕时间戳
3. 生成带有精确同步字幕的视频
"""

import os
import glob
import numpy as np
from typing import List, Dict, Optional, Tuple
import sys
import platform
import tempfile

# 检查必要的依赖
try:
    from moviepy.editor import *
except ImportError:
    print("错误：未安装moviepy库")
    print("请运行：pip install moviepy")
    sys.exit(1)

try:
    from PIL import Image, ImageDraw, ImageFont
    import PIL.Image
    if not hasattr(PIL.Image, 'ANTIALIAS'):
        PIL.Image.ANTIALIAS = PIL.Image.LANCZOS
except ImportError:
    print("错误：未安装Pillow库")
    print("请运行：pip install Pillow")
    sys.exit(1)

try:
    from gpt_tts_api import GPTTextToSpeechAPI, GPT_VOICE_OPTIONS
except ImportError:
    print("错误：无法导入gpt_tts_api模块")
    sys.exit(1)

try:
    from volcano_api import VolcanoSpeechAPI, convert_to_srt
except ImportError:
    print("错误：无法导入volcano_api模块")
    sys.exit(1)

from dotenv import load_dotenv
load_dotenv()

class VideoGeneratorV5:
    def __init__(self, gpt_api_key: str, volcano_app_id: str, volcano_access_token: str):
        """初始化视频生成器"""
        self.gpt_api = GPTTextToSpeechAPI(gpt_api_key)
        self.volcano_api = VolcanoSpeechAPI(volcano_app_id, volcano_access_token)
        self.temp_files = []
        
    def cleanup_temp_files(self):
        """清理临时文件"""
        for file_path in self.temp_files:
            try:
                # 确保file_path是字符串
                if isinstance(file_path, dict):
                    continue  # 跳过字典类型
                if isinstance(file_path, str) and os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"清理临时文件: {file_path}")
            except Exception as e:
                print(f"清理文件失败 {file_path}: {e}")
    
    def find_chinese_font(self):
        """查找中文字体"""
        system = platform.system()
        
        if system == "Windows":
            font_dirs = ["C:/Windows/Fonts/"]
            chinese_fonts = ["msyh.ttc", "simhei.ttf", "simsun.ttc", "simkai.ttf"]
        elif system == "Darwin":  # macOS
            font_dirs = ["/System/Library/Fonts/", "/Library/Fonts/", os.path.expanduser("~/Library/Fonts/")]
            chinese_fonts = ["PingFang.ttc", "STHeiti Light.ttc", "STHeiti Medium.ttc", 
                           "STHeitiSC-Light.ttc", "STHeitiSC-Medium.ttc", "Hiragino Sans GB.ttc"]
        else:  # Linux
            font_dirs = ["/usr/share/fonts/", "/usr/local/share/fonts/", os.path.expanduser("~/.fonts/")]
            chinese_fonts = ["wqy-microhei.ttc", "wqy-zenhei.ttf", "NotoSansCJK-Regular.ttc"]
        
        font_size = 24
        for font_dir in font_dirs:
            if os.path.exists(font_dir):
                for font_name in chinese_fonts:
                    font_path = os.path.join(font_dir, font_name)
                    if os.path.exists(font_path):
                        try:
                            font = ImageFont.truetype(font_path, font_size)
                            print(f"找到中文字体: {font_path}")
                            return font
                        except:
                            continue
        
        try:
            font = ImageFont.load_default()
            print("警告：使用默认字体，可能不支持中文")
            return font
        except:
            return None
    
    def generate_speech_with_gpt(self, text: str, voice: str = "alloy", 
                               model: str = "tts-1") -> Optional[str]:
        """使用GPT TTS生成语音"""
        print("\n使用GPT TTS生成语音...")
        print(f"音色: {voice}, 模型: {model}")
        
        # 调用GPT TTS API（只生成音频，不要字幕）
        result = self.gpt_api.text_to_speech(
            text=text,
            voice=voice,
            model=model,
            response_format="mp3"
        )
        
        if not result:
            print("生成语音失败")
            return None
        
        # 如果返回的是字典，提取音频路径
        if isinstance(result, dict):
            audio_path = result.get('audio_path')
            if not audio_path:
                print("未找到音频路径")
                return None
        else:
            audio_path = result
            
        self.temp_files.append(audio_path)
        print(f"✓ 音频生成成功: {audio_path}")
        
        return audio_path
    
    def generate_accurate_subtitles_with_volcano(self, audio_path: str, 
                                               words_per_line: int = 20,
                                               max_lines: int = 2) -> Optional[List[Dict]]:
        """使用火山引擎ASR生成精确的字幕时间戳"""
        print("\n使用火山引擎ASR生成精确字幕...")
        print(f"每行字数: {words_per_line}, 最大行数: {max_lines}")
        
        # 将音频转换为WAV格式（如果需要）
        if audio_path.endswith('.mp3'):
            print("转换音频格式为WAV...")
            audio_clip = AudioFileClip(audio_path)
            wav_path = tempfile.NamedTemporaryFile(suffix='.wav', delete=False).name
            audio_clip.write_audiofile(wav_path, verbose=False, logger=None)
            self.temp_files.append(wav_path)
            audio_path = wav_path
            print(f"✓ 音频已转换: {wav_path}")
        
        # 提交ASR任务
        with open(audio_path, 'rb') as audio_file:
            submit_result = self.volcano_api.submit_audio(
                audio_file=audio_file,
                words_per_line=words_per_line,
                max_lines=max_lines,
                language="zh-CN",
                use_punc=True,
                caption_type="speech"
            )
        
        if submit_result.get("code") != 0:
            print(f"❌ ASR任务提交失败: {submit_result.get('message')}")
            return None
            
        task_id = submit_result.get("id")
        print(f"✓ ASR任务已提交，任务ID: {task_id}")
        
        # 查询结果
        print("等待ASR处理...")
        result = self.volcano_api.query_result(task_id, blocking=True)
        
        if result.get("code") != 0:
            print(f"❌ ASR处理失败: {result.get('message')}")
            return None
            
        # 获取字幕
        utterances = result.get("utterances", [])
        print(f"✓ 获取到 {len(utterances)} 条字幕")
        
        # 显示字幕预览
        print("\n字幕预览：")
        for i, utt in enumerate(utterances[:5]):
            start = utt['start_time'] / 1000
            end = utt['end_time'] / 1000
            duration = end - start
            text = utt['text']
            print(f"  {i+1}. [{start:.2f}s - {end:.2f}s] ({duration:.2f}s): {text}")
        if len(utterances) > 5:
            print(f"  ... 还有 {len(utterances) - 5} 条字幕")
            
        return utterances
    
    def calculate_adaptive_font_size(self, text, max_width, font_base, min_size=40, max_size=80):
        """计算适应指定宽度的字体大小"""
        temp_img = Image.new('RGBA', (1, 1), (0, 0, 0, 0))
        temp_draw = ImageDraw.Draw(temp_img)
        
        lines = text.split('\n')
        max_line = max(lines, key=len) if lines else text
        
        low, high = min_size, max_size
        best_size = min_size
        
        while low <= high:
            mid = (low + high) // 2
            try:
                font = font_base.font_variant(size=mid)
            except:
                font_path = font_base.path if hasattr(font_base, 'path') else None
                if font_path:
                    font = ImageFont.truetype(font_path, mid)
                else:
                    font = font_base
            
            bbox = temp_draw.textbbox((0, 0), max_line, font=font)
            width = bbox[2] - bbox[0]
            
            if width <= max_width * 0.9:
                best_size = mid
                low = mid + 1
            else:
                high = mid - 1
        
        return max(min(best_size, max_size), min_size)
    
    def wrap_text(self, text, max_width, font_base, font_size):
        """根据最大宽度对文本进行换行处理"""
        try:
            font = font_base.font_variant(size=font_size)
        except:
            font_path = font_base.path if hasattr(font_base, 'path') else None
            if font_path:
                font = ImageFont.truetype(font_path, font_size)
            else:
                font = font_base
        
        temp_img = Image.new('RGBA', (1, 1), (0, 0, 0, 0))
        temp_draw = ImageDraw.Draw(temp_img)
        
        raw_lines = text.split('\n')
        lines = []
        
        for raw_line in raw_lines:
            if not raw_line.strip():
                lines.append('')
                continue
            
            bbox = temp_draw.textbbox((0, 0), raw_line, font=font)
            width = bbox[2] - bbox[0]
            
            if width <= max_width:
                lines.append(raw_line)
                continue
            
            current_line = ''
            for char in raw_line:
                test_line = current_line + char
                bbox = temp_draw.textbbox((0, 0), test_line, font=font)
                width = bbox[2] - bbox[0]
                
                if width <= max_width:
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                    current_line = char
            
            if current_line:
                lines.append(current_line)
        
        return lines
    
    def create_subtitle_image(self, text, video_width, video_height, font_base, start_time, duration):
        """使用PIL创建字幕图像"""
        img = Image.new('RGBA', (video_width, video_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        max_subtitle_width = int(video_width * 0.8)
        font_size = self.calculate_adaptive_font_size(text, max_subtitle_width, font_base)
        
        try:
            font = font_base.font_variant(size=font_size)
        except:
            font_path = font_base.path if hasattr(font_base, 'path') else None
            if font_path:
                font = ImageFont.truetype(font_path, font_size)
            else:
                font = font_base
        
        lines = self.wrap_text(text, max_subtitle_width, font_base, font_size)
        
        line_height = int(font_size * 1.5)
        text_height = len(lines) * line_height
        
        y_position = video_height - text_height - int(video_height * 0.1)
        
        for line_idx, line in enumerate(lines):
            bbox = draw.textbbox((0, 0), line, font=font)
            text_width = bbox[2] - bbox[0]
            
            x_position = (video_width - text_width) // 2
            y = y_position + line_idx * line_height
            
            # 绘制黑色描边
            for offset_x in [-3, -2, -1, 0, 1, 2, 3]:
                for offset_y in [-3, -2, -1, 0, 1, 2, 3]:
                    if offset_x != 0 or offset_y != 0:
                        draw.text(
                            (x_position + offset_x, y + offset_y),
                            line,
                            font=font,
                            fill=(0, 0, 0, 255)
                        )
            
            # 绘制黄色文本
            draw.text(
                (x_position, y),
                line,
                font=font,
                fill=(255, 255, 0, 255)
            )
        
        img_array = np.array(img)
        subtitle_clip = ImageClip(img_array, duration=duration)
        subtitle_clip = subtitle_clip.set_start(start_time)
        
        return subtitle_clip
    
    def read_images_from_folder(self, folder_path: str) -> List[str]:
        """读取文件夹下的所有图片"""
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.gif']
        image_files = []
        
        for ext in image_extensions:
            pattern = os.path.join(folder_path, ext)
            image_files.extend(glob.glob(pattern))
            
        image_files.sort()
        
        print(f"找到 {len(image_files)} 张图片在文件夹: {folder_path}")
        for img in image_files:
            print(f"  - {os.path.basename(img)}")
            
        return image_files
    
    def process_image_for_video(self, img_path: str, target_width: int = 1920, target_height: int = 1080) -> np.ndarray:
        """处理图片以适应视频尺寸"""
        try:
            pil_img = Image.open(img_path)
            
            if pil_img.mode != 'RGB':
                pil_img = pil_img.convert('RGB')
            
            img_width, img_height = pil_img.size
            width_ratio = target_width / img_width
            height_ratio = target_height / img_height
            
            scale_ratio = max(width_ratio, height_ratio)
            
            new_width = int(img_width * scale_ratio)
            new_height = int(img_height * scale_ratio)
            
            resample = PIL.Image.LANCZOS if hasattr(PIL.Image, 'LANCZOS') else PIL.Image.ANTIALIAS
            pil_img = pil_img.resize((new_width, new_height), resample)
            
            left = (new_width - target_width) // 2
            top = (new_height - target_height) // 2
            right = left + target_width
            bottom = top + target_height
            
            pil_img = pil_img.crop((left, top, right, bottom))
            
            return np.array(pil_img)
            
        except Exception as e:
            print(f"处理图片 {img_path} 时出错: {e}")
            return np.zeros((target_height, target_width, 3), dtype=np.uint8)
    
    def save_srt_file(self, subtitles: List[Dict], output_path: str) -> str:
        """保存SRT格式字幕文件"""
        srt_content = convert_to_srt(subtitles)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(srt_content)
            
        print(f"✓ 字幕已保存到: {output_path}")
        return output_path
    
    def create_video_with_subtitles(self, 
                                  images: List[str], 
                                  audio_path: str, 
                                  subtitles: List[Dict],
                                  output_path: str,
                                  image_duration: Optional[float] = None,
                                  transition_duration: float = 0.5):
        """创建带字幕的视频"""
        print("\n开始创建视频...")
        
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
            
        try:
            audio = AudioFileClip(audio_path)
            audio_duration = audio.duration
            print(f"✓ 音频加载成功，时长: {audio_duration:.2f}秒")
        except Exception as e:
            raise Exception(f"加载音频失败: {e}")
        
        if image_duration is None:
            num_images = len(images)
            if num_images > 0:
                if num_images < audio_duration / 3:
                    min_duration_per_image = 3.0
                    total_duration_needed = num_images * min_duration_per_image
                    
                    if total_duration_needed < audio_duration:
                        cycles = int(audio_duration / total_duration_needed) + 1
                        images = images * cycles
                        num_images = len(images)
                        print(f"图片数量较少，将循环使用 {cycles} 次")
                
                overlap_time = transition_duration * (num_images - 1)
                image_duration = (audio_duration + overlap_time) / num_images
                image_duration = max(image_duration, 1.0)
            else:
                image_duration = 5.0
                
        print(f"图片数量: {len(images)} 张")
        print(f"每张图片显示: {image_duration:.2f}秒")
        print(f"转场时长: {transition_duration:.2f}秒")
        
        original_images = images.copy()
        
        clips = []
        current_time = 0
        image_index = 0
        
        if original_images:
            print("添加视频封面帧...")
            cover_img_array = self.process_image_for_video(original_images[0])
            cover_clip = ImageClip(cover_img_array, duration=0.1)
            cover_clip = cover_clip.set_start(0)
            clips.append(cover_clip)
            current_time = 0.1
        
        while current_time < audio_duration:
            img_path = original_images[image_index % len(original_images)]
            
            if current_time + image_duration > audio_duration:
                actual_duration = audio_duration - current_time + transition_duration
            else:
                actual_duration = image_duration
            
            print(f"处理图片 {image_index + 1}: {os.path.basename(img_path)} (时间: {current_time:.1f}s - {current_time + actual_duration - transition_duration:.1f}s)")
            
            try:
                img_array = self.process_image_for_video(img_path)
                img_clip = ImageClip(img_array, duration=actual_duration)
                img_clip = img_clip.set_start(current_time)
                
                if current_time + actual_duration < audio_duration:
                    if image_index > 0:
                        img_clip = img_clip.crossfadein(transition_duration)
                    img_clip = img_clip.crossfadeout(transition_duration)
                else:
                    if image_index > 0:
                        img_clip = img_clip.crossfadein(transition_duration)
                
                clips.append(img_clip)
                current_time += actual_duration - transition_duration
                image_index += 1
                
            except Exception as e:
                print(f"⚠️ 处理图片失败: {e}")
                image_index += 1
        
        if clips:
            print(f"\n✓ 成功处理 {len(clips)} 张图片")
            video = CompositeVideoClip(clips, size=(1920, 1080))
        else:
            print("⚠️ 没有可用的图片，创建黑色背景")
            video = ColorClip(size=(1920, 1080), color=(0,0,0), duration=audio_duration)
        
        video = video.set_duration(audio_duration)
        
        print("添加音频...")
        video = video.set_audio(audio)
        
        subtitle_clips = []
        
        font_base = self.find_chinese_font()
        if font_base is None:
            print("⚠️ 无法找到字体，将生成无字幕版本")
        else:
            print("\n添加字幕...")
            for i, subtitle in enumerate(subtitles):
                start_time = subtitle.get('start_time', 0) / 1000  # 毫秒转秒
                end_time = subtitle.get('end_time', 0) / 1000
                text = subtitle.get('text', '').strip()
                
                if text and end_time > start_time:
                    duration = end_time - start_time
                    print(f"字幕 {i+1}: {text[:20]}... ({start_time:.1f}s - {end_time:.1f}s)")
                    
                    try:
                        subtitle_clip = self.create_subtitle_image(
                            text, 1920, 1080, font_base, start_time, duration
                        )
                        subtitle_clips.append(subtitle_clip)
                    except Exception as e:
                        print(f"⚠️ 创建字幕 {i+1} 失败: {e}")
        
        if subtitle_clips:
            print(f"✓ 成功添加 {len(subtitle_clips)} 条字幕")
            all_clips = [video] + subtitle_clips
            final_video = CompositeVideoClip(all_clips)
        else:
            print("⚠️ 生成无字幕版本")
            final_video = video
        
        print(f"\n开始渲染视频...")
        print(f"输出路径: {output_path}")
        
        import multiprocessing
        cpu_count = multiprocessing.cpu_count()
        
        print(f"使用快速渲染模式（CPU核心数: {cpu_count}）")
        
        try:
            final_video.write_videofile(
                output_path,
                fps=24,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                threads=cpu_count,
                preset='ultrafast',
                bitrate='2000k',
                ffmpeg_params=[
                    '-movflags', '+faststart',
                    '-pix_fmt', 'yuv420p'
                ]
            )
            print(f"\n✅ 视频生成成功: {output_path}")
            
        except Exception as e:
            print(f"\n❌ 视频渲染失败: {e}")
            raise

def main():
    """主函数"""
    # 从环境变量读取配置
    gpt_api_key = os.getenv('GPT_API_KEY', 'sk-kmhk2OUYmq5SskSR2e2f1d9cCb5c4e9dB7A3118116548fDe')
    volcano_app_id = os.getenv('VOLCANO_APP_ID')
    volcano_access_token = os.getenv('VOLCANO_ACCESS_TOKEN')
    
    if not gpt_api_key:
        print("错误：请设置GPT_API_KEY环境变量")
        return
        
    if not volcano_app_id or not volcano_access_token:
        print("错误：请设置VOLCANO_APP_ID和VOLCANO_ACCESS_TOKEN环境变量")
        return
    
    # 设置参数
    image_folder = "矩阵产品"
    output_video = "output_video_v5_accurate.mp4"
    
    if not os.path.exists(image_folder):
        print(f"错误：文件夹 '{image_folder}' 不存在")
        return
    
    # 初始化视频生成器
    generator = VideoGeneratorV5(gpt_api_key, volcano_app_id, volcano_access_token)
    
    try:
        # 1. 读取图片
        images = generator.read_images_from_folder(image_folder)
        if not images:
            print("错误：未找到任何图片")
            return
        
        # 2. 显示GPT语音选项
        print("\n可用的GPT TTS语音选项：")
        for category, voices in GPT_VOICE_OPTIONS.items():
            print(f"\n{category}:")
            for voice_id, description in voices.items():
                print(f"  - {voice_id}: {description}")
        
        # 3. 选择语音
        print("\n请选择语音（直接回车使用默认 'alloy'）：")
        voice_input = input().strip()
        voice = voice_input if voice_input else "alloy"
        
        # 4. 选择模型
        print("\n请选择模型（1: 标准质量 tts-1, 2: 高质量 tts-1-hd，直接回车使用标准）：")
        model_input = input().strip()
        model = "tts-1-hd" if model_input == "2" else "tts-1"
        
        # 5. 设置字幕参数
        print("\n字幕设置（直接回车使用默认值）：")
        print("每行字数（默认20）：")
        words_input = input().strip()
        words_per_line = int(words_input) if words_input.isdigit() else 20
        
        # 6. 获取文案
        print("\n请输入要配音的文案（输入完成后按两次回车）：")
        lines = []
        empty_count = 0
        
        while empty_count < 2:
            line = input()
            if line:
                lines.append(line)
                empty_count = 0
            else:
                empty_count += 1
        
        text = '\n'.join(lines[:-1] if lines and lines[-1] == '' else lines)
        
        if not text.strip():
            print("错误：未输入文案")
            return
        
        print(f"\n输入的文案：\n{text}")
        print(f"选择的语音：{voice}")
        print(f"选择的模型：{model}")
        print(f"每行字数：{words_per_line}")
        
        # 7. 生成语音（使用GPT）
        audio_path = generator.generate_speech_with_gpt(text, voice, model)
        if not audio_path:
            print("生成语音失败")
            return
        
        # 8. 生成精确字幕（使用火山引擎ASR）
        subtitles = generator.generate_accurate_subtitles_with_volcano(
            audio_path, 
            words_per_line=words_per_line,
            max_lines=2
        )
        
        if not subtitles:
            print("警告：字幕生成失败，将生成无字幕视频")
            subtitles = []
        
        # 9. 保存字幕文件
        if subtitles:
            srt_path = output_video.replace('.mp4', '.srt')
            generator.save_srt_file(subtitles, srt_path)
        
        # 10. 创建视频
        generator.create_video_with_subtitles(
            images=images,
            audio_path=audio_path,
            subtitles=subtitles,
            output_path=output_video,
            transition_duration=0.5
        )
        
        print(f"\n✅ 所有任务完成！")
        print(f"视频文件：{output_video}")
        if subtitles:
            print(f"字幕文件：{srt_path}")
        
    except Exception as e:
        print(f"\n❌ 发生错误：{e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        generator.cleanup_temp_files()

if __name__ == "__main__":
    main()
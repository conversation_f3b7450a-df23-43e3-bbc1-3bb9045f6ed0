#!/usr/bin/env python3
"""
图片配音视频生成脚本 V3
功能：读取矩阵产品文件夹下的图片，根据文案生成语音和字幕，然后生成视频
使用GPT TTS API替代Volcano TTS，提供更好的英文语音效果
"""

import os
import glob
import numpy as np
from typing import List, Dict, Optional, Tuple
import sys
import platform

# 检查必要的依赖
try:
    from moviepy.editor import *
except ImportError:
    print("错误：未安装moviepy库")
    print("请运行：pip install moviepy")
    sys.exit(1)

try:
    from PIL import Image, ImageDraw, ImageFont
    import PIL.Image
    # 兼容性处理
    if not hasattr(PIL.Image, 'ANTIALIAS'):
        PIL.Image.ANTIALIAS = PIL.Image.LANCZOS
except ImportError:
    print("错误：未安装Pillow库")
    print("请运行：pip install Pillow")
    sys.exit(1)

try:
    from gpt_tts_api import GPTTextToSpeechAPI, convert_gpt_subtitles_to_srt, GPT_VOICE_OPTIONS
except ImportError:
    print("错误：无法导入gpt_tts_api模块")
    print("请确保gpt_tts_api.py文件在同一目录下")
    sys.exit(1)

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class VideoGeneratorV3:
    def __init__(self, gpt_api_key: str):
        """初始化视频生成器"""
        self.api = GPTTextToSpeechAPI(gpt_api_key)
        self.temp_files = []  # 跟踪临时文件以便清理
        
    def cleanup_temp_files(self):
        """清理临时文件"""
        for file_path in self.temp_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"清理临时文件: {file_path}")
            except Exception as e:
                print(f"清理文件失败 {file_path}: {e}")
    
    def find_chinese_font(self):
        """
        在系统中查找支持中文的字体
        
        返回:
            PIL.ImageFont: 找到的字体对象，如果未找到则返回None
        """
        system = platform.system()
        
        if system == "Windows":
            font_dirs = ["C:/Windows/Fonts/"]
            chinese_font_names = [
                "msyh.ttc",  # 微软雅黑
                "simhei.ttf",  # 黑体
                "simsun.ttc",  # 宋体
                "simkai.ttf",  # 楷体
            ]
        elif system == "Darwin":  # macOS
            font_dirs = [
                "/System/Library/Fonts/",
                "/Library/Fonts/",
                os.path.expanduser("~/Library/Fonts/"),
            ]
            chinese_font_names = [
                "PingFang.ttc",  # 苹方
                "STHeiti Light.ttc",  # 华文黑体  
                "STHeiti Medium.ttc",  # 华文黑体中
                "STHeitiSC-Light.ttc",  # 华文黑体-简
                "STHeitiSC-Medium.ttc",  # 华文黑体-简 中等
                "Hiragino Sans GB.ttc",  # 冬青黑体
            ]
        else:  # Linux
            font_dirs = [
                "/usr/share/fonts/",
                "/usr/local/share/fonts/",
                os.path.expanduser("~/.fonts/"),
            ]
            chinese_font_names = [
                "wqy-microhei.ttc",  # 文泉驿微米黑
                "wqy-zenhei.ttf",  # 文泉驿正黑
                "NotoSansCJK-Regular.ttc",  # 思源黑体
                "NotoSerifCJK-Regular.ttc",  # 思源宋体
            ]
        
        # 寻找字体
        font_size = 24
        for font_dir in font_dirs:
            if os.path.exists(font_dir):
                for font_name in chinese_font_names:
                    font_path = os.path.join(font_dir, font_name)
                    if os.path.exists(font_path):
                        try:
                            font = ImageFont.truetype(font_path, font_size)
                            print(f"找到中文字体: {font_path}")
                            return font
                        except Exception:
                            continue
        
        # 如果找不到中文字体，尝试使用默认字体
        try:
            font = ImageFont.load_default()
            print("警告：使用默认字体，可能不支持中文")
            return font
        except:
            return None
    
    def calculate_adaptive_font_size(self, text, max_width, font_base, min_size=40, max_size=80):
        """计算适应指定宽度的字体大小"""
        # 创建临时图像进行测量
        temp_img = Image.new('RGBA', (1, 1), (0, 0, 0, 0))
        temp_draw = ImageDraw.Draw(temp_img)
        
        # 如果文本包含换行符，找出最长的一行
        lines = text.split('\n')
        max_line = max(lines, key=len) if lines else text
        
        # 二分查找最佳字体大小
        low, high = min_size, max_size
        best_size = min_size
        
        while low <= high:
            mid = (low + high) // 2
            try:
                font = font_base.font_variant(size=mid)
            except:
                # 如果font_variant不可用，重新创建字体
                font_path = font_base.path if hasattr(font_base, 'path') else None
                if font_path:
                    font = ImageFont.truetype(font_path, mid)
                else:
                    font = font_base
            
            # 测量宽度
            bbox = temp_draw.textbbox((0, 0), max_line, font=font)
            width = bbox[2] - bbox[0]
            
            if width <= max_width * 0.9:  # 留10%的余量
                best_size = mid
                low = mid + 1
            else:
                high = mid - 1
        
        return max(min(best_size, max_size), min_size)
    
    def wrap_text(self, text, max_width, font_base, font_size):
        """根据最大宽度对文本进行换行处理"""
        # 创建指定大小的字体
        try:
            font = font_base.font_variant(size=font_size)
        except:
            font_path = font_base.path if hasattr(font_base, 'path') else None
            if font_path:
                font = ImageFont.truetype(font_path, font_size)
            else:
                font = font_base
        
        # 创建临时图像进行测量
        temp_img = Image.new('RGBA', (1, 1), (0, 0, 0, 0))
        temp_draw = ImageDraw.Draw(temp_img)
        
        # 如果文本已包含换行符，先按换行符分割
        raw_lines = text.split('\n')
        lines = []
        
        for raw_line in raw_lines:
            if not raw_line.strip():
                lines.append('')
                continue
            
            # 测量当前行宽度
            bbox = temp_draw.textbbox((0, 0), raw_line, font=font)
            width = bbox[2] - bbox[0]
            
            # 如果行宽度小于最大宽度，直接添加
            if width <= max_width:
                lines.append(raw_line)
                continue
            
            # 否则需要拆分行
            current_line = ''
            for char in raw_line:
                test_line = current_line + char
                bbox = temp_draw.textbbox((0, 0), test_line, font=font)
                width = bbox[2] - bbox[0]
                
                if width <= max_width:
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                    current_line = char
            
            if current_line:
                lines.append(current_line)
        
        return lines
    
    def create_subtitle_image(self, text, video_width, video_height, font_base, start_time, duration):
        """使用PIL创建字幕图像"""
        # 创建透明背景图像
        img = Image.new('RGBA', (video_width, video_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 计算字幕最大宽度（视频宽度的80%）
        max_subtitle_width = int(video_width * 0.8)
        
        # 计算自适应字体大小
        font_size = self.calculate_adaptive_font_size(text, max_subtitle_width, font_base)
        
        # 创建指定大小的字体
        try:
            font = font_base.font_variant(size=font_size)
        except:
            font_path = font_base.path if hasattr(font_base, 'path') else None
            if font_path:
                font = ImageFont.truetype(font_path, font_size)
            else:
                font = font_base
        
        # 处理文本换行
        lines = self.wrap_text(text, max_subtitle_width, font_base, font_size)
        
        # 计算文本总高度
        line_height = int(font_size * 1.5)
        text_height = len(lines) * line_height
        
        # 计算起始y坐标（底部上方10%位置）
        y_position = video_height - text_height - int(video_height * 0.1)
        
        # 绘制每一行字幕
        for line_idx, line in enumerate(lines):
            # 计算当前行的边界框
            bbox = draw.textbbox((0, 0), line, font=font)
            text_width = bbox[2] - bbox[0]
            
            # 计算x坐标（居中）
            x_position = (video_width - text_width) // 2
            y = y_position + line_idx * line_height
            
            # 绘制黑色描边（更粗的描边效果）
            for offset_x in [-3, -2, -1, 0, 1, 2, 3]:
                for offset_y in [-3, -2, -1, 0, 1, 2, 3]:
                    if offset_x != 0 or offset_y != 0:
                        draw.text(
                            (x_position + offset_x, y + offset_y),
                            line,
                            font=font,
                            fill=(0, 0, 0, 255),  # 黑色描边
                        )
            
            # 绘制黄色文本
            draw.text(
                (x_position, y),
                line,
                font=font,
                fill=(255, 255, 0, 255),  # 黄色
            )
        
        # 转换为numpy数组
        img_array = np.array(img)
        
        # 创建ImageClip
        subtitle_clip = ImageClip(img_array, duration=duration)
        subtitle_clip = subtitle_clip.set_start(start_time)
        
        return subtitle_clip
                
    def read_images_from_folder(self, folder_path: str) -> List[str]:
        """读取文件夹下的所有图片"""
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.gif']
        image_files = []
        
        for ext in image_extensions:
            pattern = os.path.join(folder_path, ext)
            image_files.extend(glob.glob(pattern))
            
        # 按文件名排序
        image_files.sort()
        
        print(f"找到 {len(image_files)} 张图片在文件夹: {folder_path}")
        for img in image_files:
            print(f"  - {os.path.basename(img)}")
            
        return image_files
    
    def split_long_subtitle(self, subtitle: Dict, max_duration: float = 3.0, max_chars: int = 20) -> List[Dict]:
        """将长字幕分割成多个短字幕
        
        Args:
            subtitle: 原始字幕
            max_duration: 每条字幕最长显示时间（秒）
            max_chars: 每条字幕最多字符数
            
        Returns:
            分割后的字幕列表
        """
        text = subtitle.get('content', '').strip()
        start_time = subtitle.get('start', 0)
        end_time = subtitle.get('end', 0)
        total_duration = (end_time - start_time) / 1000  # 转换为秒
        
        if not text or total_duration <= 0:
            return []
        
        # 如果字幕已经很短，也要去除标点后返回
        if total_duration <= max_duration and len(text) <= max_chars:
            # 去除末尾标点
            text = text.rstrip('。！？，；、.!?,;')
            subtitle_copy = subtitle.copy()
            subtitle_copy['content'] = text
            subtitle_copy['text'] = text  # 保持兼容性
            return [subtitle_copy]
        
        # 分割文本
        # 优先按标点符号分割
        punctuations = ['。', '！', '？', '，', '；', '.', '!', '?', ',', ';']
        
        # 查找所有标点符号的位置
        split_points = []
        for i, char in enumerate(text):
            if char in punctuations and i < len(text) - 1:  # 不在末尾的标点
                split_points.append(i + 1)  # 标点后分割
        
        # 如果没有标点符号，按字符数分割（考虑英文单词边界）
        if not split_points:
            # 检查文本是否包含英文
            import re
            has_english = bool(re.search(r'[a-zA-Z]', text))
            
            if has_english:
                # 对于包含英文的文本，在单词边界处分割
                words = text.split()
                current_length = 0
                current_pos = 0
                
                for i, word in enumerate(words):
                    word_length = len(word)
                    # 加上空格的长度（除了第一个单词）
                    if i > 0:
                        word_length += 1
                        current_pos += 1
                    
                    if current_length + word_length > max_chars and current_length > 0:
                        # 在当前位置（单词开始前）分割
                        split_points.append(current_pos)
                        current_length = word_length
                    else:
                        current_length += word_length
                    
                    current_pos += len(word)
            else:
                # 对于纯中文文本，按字符数分割
                for i in range(max_chars, len(text), max_chars):
                    split_points.append(i)
        
        # 添加文本开始和结束位置
        split_points = [0] + split_points + [len(text)]
        
        # 生成文本片段
        text_segments = []
        for i in range(len(split_points) - 1):
            segment = text[split_points[i]:split_points[i + 1]].strip()
            # 去掉末尾的标点符号
            segment = segment.rstrip('。！？，；、.!?,;')
            if segment:  # 忽略空片段
                text_segments.append(segment)
        
        # 如果没有有效片段，返回原始字幕
        if not text_segments:
            return [subtitle]
        
        # 计算每个片段的时长
        # 方法1：按字符比例分配时间
        total_chars = sum(len(seg) for seg in text_segments)
        
        # 生成新的字幕列表
        new_subtitles = []
        current_time = start_time
        
        for i, segment in enumerate(text_segments):
            # 计算该片段的时长（按字符比例）
            char_ratio = len(segment) / total_chars
            segment_duration = total_duration * char_ratio * 1000  # 转回毫秒
            
            # 限制最大时长
            if segment_duration > max_duration * 1000:
                segment_duration = max_duration * 1000
            
            # 确保不超过原始结束时间
            segment_end = min(current_time + segment_duration, end_time)
            
            # 再次确保去除末尾标点
            segment = segment.rstrip('。！？，；、.!?,;')
            
            new_subtitle = {
                'content': segment,
                'text': segment,  # 保持兼容性
                'start': int(current_time),
                'end': int(segment_end),
                'start_time': int(current_time),  # 兼容性
                'end_time': int(segment_end),  # 兼容性
                'index': subtitle.get('index', 1) + i
            }
            
            new_subtitles.append(new_subtitle)
            current_time = segment_end
            
            # 如果已经到达结束时间，停止
            if current_time >= end_time:
                break
        
        return new_subtitles
    
    def generate_speech_and_subtitles(self, 
                                    text: str, 
                                    voice: str = "alloy",
                                    model: str = "tts-1",
                                    response_format: str = "mp3") -> Optional[Tuple[str, List[Dict]]]:
        """使用GPT TTS生成语音和字幕"""
        print("\n开始使用GPT TTS生成语音和字幕...")
        
        # 调用GPT TTS API
        result = self.api.text_to_speech_with_subtitles(
            text=text,
            voice=voice,
            model=model,
            response_format=response_format
        )
        
        if not result:
            print("生成语音失败")
            return None
            
        audio_path = result['audio_path']
        self.temp_files.append(audio_path)
        
        # 提取字幕数据
        original_subtitles = result.get('subtitles', [])
        
        if not original_subtitles:
            print("未获取到字幕数据")
            return audio_path, []
            
        print(f"成功生成音频: {audio_path}")
        print(f"原始字幕数量: {len(original_subtitles)} 条")
        
        # 分割长字幕
        all_subtitles = []
        for subtitle in original_subtitles:
            split_subs = self.split_long_subtitle(subtitle, max_duration=3.0, max_chars=30)
            all_subtitles.extend(split_subs)
        
        print(f"分割后字幕数量: {len(all_subtitles)} 条")
        
        # 显示字幕预览
        print("\n字幕预览：")
        for i, sub in enumerate(all_subtitles[:5]):  # 只显示前5条
            start = sub['start'] / 1000
            end = sub['end'] / 1000
            duration = end - start
            content = sub.get('content', sub.get('text', ''))
            print(f"  {i+1}. [{start:.1f}s-{end:.1f}s] ({duration:.1f}s): {content[:20]}...")
        if len(all_subtitles) > 5:
            print(f"  ... 还有 {len(all_subtitles) - 5} 条字幕")
        
        return audio_path, all_subtitles
    
    def save_srt_file(self, subtitles: List[Dict], output_path: str) -> str:
        """保存SRT格式字幕文件"""
        srt_content = convert_gpt_subtitles_to_srt(subtitles)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(srt_content)
            
        print(f"字幕已保存到: {output_path}")
        return output_path
    
    def process_image_for_video(self, img_path: str, target_width: int = 1920, target_height: int = 1080) -> np.ndarray:
        """处理图片以适应视频尺寸"""
        try:
            # 使用PIL打开图片
            pil_img = Image.open(img_path)
            
            # 转换为RGB模式
            if pil_img.mode != 'RGB':
                pil_img = pil_img.convert('RGB')
            
            # 计算缩放比例
            img_width, img_height = pil_img.size
            width_ratio = target_width / img_width
            height_ratio = target_height / img_height
            
            # 使用较大的比例，确保图片填满画面
            scale_ratio = max(width_ratio, height_ratio)
            
            # 计算新尺寸
            new_width = int(img_width * scale_ratio)
            new_height = int(img_height * scale_ratio)
            
            # 缩放图片
            resample = PIL.Image.LANCZOS if hasattr(PIL.Image, 'LANCZOS') else PIL.Image.ANTIALIAS
            pil_img = pil_img.resize((new_width, new_height), resample)
            
            # 裁剪到目标尺寸（中心裁剪）
            left = (new_width - target_width) // 2
            top = (new_height - target_height) // 2
            right = left + target_width
            bottom = top + target_height
            
            pil_img = pil_img.crop((left, top, right, bottom))
            
            # 转换为numpy数组
            return np.array(pil_img)
            
        except Exception as e:
            print(f"处理图片 {img_path} 时出错: {e}")
            # 返回一个黑色图片作为占位
            return np.zeros((target_height, target_width, 3), dtype=np.uint8)
    
    def create_video_with_subtitles(self, 
                                  images: List[str], 
                                  audio_path: str, 
                                  subtitles: List[Dict],
                                  output_path: str,
                                  image_duration: Optional[float] = None,
                                  transition_duration: float = 0.5):
        """创建带字幕的视频"""
        print("\n开始创建视频...")
        
        # 检查音频文件
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
            
        # 加载音频
        try:
            audio = AudioFileClip(audio_path)
            audio_duration = audio.duration
            print(f"✓ 音频加载成功，时长: {audio_duration:.2f}秒")
        except Exception as e:
            raise Exception(f"加载音频失败: {e}")
        
        # 计算每张图片的显示时长
        if image_duration is None:
            num_images = len(images)
            if num_images > 0:
                # 方法1：如果图片数量较少，循环使用图片
                if num_images < audio_duration / 3:  # 如果每张图片显示超过3秒
                    # 计算需要循环多少次
                    min_duration_per_image = 3.0  # 每张图片至少显示3秒
                    total_duration_needed = num_images * min_duration_per_image
                    
                    if total_duration_needed < audio_duration:
                        # 需要循环图片
                        cycles = int(audio_duration / total_duration_needed) + 1
                        images = images * cycles  # 复制图片列表
                        num_images = len(images)
                        print(f"图片数量较少，将循环使用 {cycles} 次")
                
                # 重新计算每张图片的显示时长
                # 确保图片覆盖整个音频时长
                # 计算方式：(音频总时长 + 转场重叠时间) / 图片数量
                overlap_time = transition_duration * (num_images - 1)
                image_duration = (audio_duration + overlap_time) / num_images
                image_duration = max(image_duration, 1.0)  # 至少显示1秒
            else:
                image_duration = 5.0
                
        print(f"图片数量: {len(images)} 张")
        print(f"每张图片显示: {image_duration:.2f}秒")
        print(f"转场时长: {transition_duration:.2f}秒")
        
        # 验证时间计算
        total_time = len(images) * image_duration - (len(images) - 1) * transition_duration
        print(f"预计视频总时长: {total_time:.2f}秒（应等于音频时长 {audio_duration:.2f}秒）")
        
        # 保存原始图片列表（用于循环显示）
        original_images = images.copy()
        
        # 创建图片片段
        clips = []
        current_time = 0
        image_index = 0
        
        # 添加一个静态的封面帧（使用第一张图片，持续0.1秒）
        # 这确保视频的第一帧是清晰的图片而不是黑屏
        if original_images:
            print("添加视频封面帧...")
            cover_img_array = self.process_image_for_video(original_images[0])
            cover_clip = ImageClip(cover_img_array, duration=0.1)
            cover_clip = cover_clip.set_start(0)
            clips.append(cover_clip)
            current_time = 0.1  # 从0.1秒开始正常的图片循环
        
        # 持续添加图片直到覆盖整个音频时长
        while current_time < audio_duration:
            # 获取当前图片（循环使用）
            img_path = original_images[image_index % len(original_images)]
            
            # 计算这张图片的实际显示时长
            # 如果是最后一张图片，调整时长以精确匹配音频结束
            if current_time + image_duration > audio_duration:
                actual_duration = audio_duration - current_time + transition_duration
            else:
                actual_duration = image_duration
            
            print(f"处理图片 {image_index + 1}: {os.path.basename(img_path)} (时间: {current_time:.1f}s - {current_time + actual_duration - transition_duration:.1f}s)")
            
            try:
                # 使用自定义的图片处理函数
                img_array = self.process_image_for_video(img_path)
                
                # 创建图片片段
                img_clip = ImageClip(img_array, duration=actual_duration)
                
                # 设置开始时间
                img_clip = img_clip.set_start(current_time)
                
                # 添加淡入淡出效果
                if current_time + actual_duration < audio_duration:
                    # 第一张图片不添加淡入效果（避免第一帧黑屏）
                    if image_index > 0:
                        img_clip = img_clip.crossfadein(transition_duration)
                    img_clip = img_clip.crossfadeout(transition_duration)
                else:
                    # 最后一张图片
                    if image_index > 0:
                        img_clip = img_clip.crossfadein(transition_duration)
                
                clips.append(img_clip)
                
                # 更新时间和索引
                current_time += actual_duration - transition_duration
                image_index += 1
                
            except Exception as e:
                print(f"⚠️ 处理图片失败: {e}")
                image_index += 1  # 跳过失败的图片
        
        # 合成视频
        if clips:
            print(f"\n✓ 成功处理 {len(clips)} 张图片")
            video = CompositeVideoClip(clips, size=(1920, 1080))
        else:
            print("⚠️ 没有可用的图片，创建黑色背景")
            video = ColorClip(size=(1920, 1080), color=(0,0,0), duration=audio_duration)
        
        # 确保视频时长与音频一致
        video = video.set_duration(audio_duration)
        
        # 添加音频
        print("添加音频...")
        video = video.set_audio(audio)
        
        # 准备字幕
        subtitle_clips = []
        
        # 获取中文字体
        font_base = self.find_chinese_font()
        if font_base is None:
            print("⚠️ 无法找到字体，将生成无字幕版本")
        else:
            print("\n添加字幕...")
            # 创建字幕片段
            for i, subtitle in enumerate(subtitles):
                start_time = subtitle.get('start', subtitle.get('start_time', 0)) / 1000  # 毫秒转秒
                end_time = subtitle.get('end', subtitle.get('end_time', 0)) / 1000
                text = subtitle.get('content', subtitle.get('text', '')).strip()
                
                if text and end_time > start_time:
                    duration = end_time - start_time
                    print(f"字幕 {i+1}: {text[:20]}... ({start_time:.1f}s - {end_time:.1f}s)")
                    
                    try:
                        # 使用PIL创建字幕图像
                        subtitle_clip = self.create_subtitle_image(
                            text, 1920, 1080, font_base, start_time, duration
                        )
                        subtitle_clips.append(subtitle_clip)
                    except Exception as e:
                        print(f"⚠️ 创建字幕 {i+1} 失败: {e}")
        
        # 合成最终视频
        if subtitle_clips:
            print(f"✓ 成功添加 {len(subtitle_clips)} 条字幕")
            all_clips = [video] + subtitle_clips
            final_video = CompositeVideoClip(all_clips)
        else:
            print("⚠️ 生成无字幕版本")
            final_video = video
        
        # 保存封面图（使用第一张图片）
        if original_images:
            try:
                print("\n生成视频封面...")
                thumbnail_path = original_images[0]
                # 创建封面图片
                thumbnail_img = self.process_image_for_video(thumbnail_path, 1920, 1080)
                # 保存封面图片
                cover_path = output_path.replace('.mp4', '_cover.jpg')
                Image.fromarray(thumbnail_img).save(cover_path, 'JPEG', quality=90)
                print(f"✓ 封面图片已保存: {cover_path}")
            except Exception as e:
                print(f"⚠️ 生成封面图失败: {e}")
        
        # 输出视频
        print(f"\n开始渲染视频...")
        print(f"输出路径: {output_path}")
        
        # 检测系统CPU核心数
        import multiprocessing
        cpu_count = multiprocessing.cpu_count()
        
        # 根据用户选择或默认设置选择渲染质量
        render_quality = os.getenv('RENDER_QUALITY', 'fast').lower()
        
        if render_quality == 'fast':
            print(f"使用快速渲染模式（CPU核心数: {cpu_count}）")
            print("预计时间：较快（质量略低）")
            
            try:
                # 添加额外的编码参数以确保封面正确显示
                final_video.write_videofile(
                    output_path,
                    fps=24,  # 降低帧率
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile='temp-audio.m4a',
                    remove_temp=True,
                    threads=cpu_count,  # 使用所有CPU核心
                    preset='ultrafast',  # 最快的预设
                    bitrate='2000k',  # 降低比特率
                    ffmpeg_params=[
                        '-movflags', '+faststart',  # 优化流媒体播放
                        '-pix_fmt', 'yuv420p'  # 确保兼容性
                    ]
                )
                print(f"\n✅ 视频生成成功（快速模式）: {output_path}")
                
            except Exception as e:
                print(f"\n❌ 快速渲染失败: {e}")
                raise
                
        elif render_quality == 'balanced':
            print(f"使用平衡渲染模式（CPU核心数: {cpu_count}）")
            print("预计时间：适中（质量较好）")
            
            try:
                final_video.write_videofile(
                    output_path,
                    fps=30,
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile='temp-audio.m4a',
                    remove_temp=True,
                    threads=cpu_count,  # 使用所有CPU核心
                    preset='faster',  # 较快的预设
                    bitrate='3000k',
                    ffmpeg_params=[
                        '-movflags', '+faststart',
                        '-pix_fmt', 'yuv420p'
                    ]
                )
                print(f"\n✅ 视频生成成功（平衡模式）: {output_path}")
                
            except Exception as e:
                print(f"\n❌ 平衡渲染失败: {e}")
                raise
                
        else:  # high quality
            print(f"使用高质量渲染模式（CPU核心数: {cpu_count}）")
            print("预计时间：较慢（质量最高）")
            
            try:
                final_video.write_videofile(
                    output_path,
                    fps=30,
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile='temp-audio.m4a',
                    remove_temp=True,
                    threads=cpu_count,
                    preset='medium',
                    bitrate='5000k',
                    ffmpeg_params=[
                        '-movflags', '+faststart',
                        '-pix_fmt', 'yuv420p'
                    ]
                )
                print(f"\n✅ 视频生成成功（高质量模式）: {output_path}")
                
            except Exception as e:
                print(f"\n❌ 高质量渲染失败: {e}")
                # 尝试使用备用编码器
                print("尝试使用备用渲染设置...")
                try:
                    final_video.write_videofile(
                        output_path,
                        fps=24,
                        codec='mpeg4',
                        audio_codec='libmp3lame',
                        temp_audiofile='temp-audio.mp3',
                        remove_temp=True
                    )
                    print(f"\n✅ 视频生成成功（备用设置）: {output_path}")
                except Exception as e2:
                    print(f"\n❌ 备用渲染也失败: {e2}")
                    raise

def main():
    """主函数"""
    # 从环境变量读取GPT API密钥
    gpt_api_key = os.getenv('GPT_API_KEY', 'sk-kmhk2OUYmq5SskSR2e2f1d9cCb5c4e9dB7A3118116548fDe')
    
    if not gpt_api_key:
        print("错误：请设置GPT_API_KEY环境变量或在.env文件中配置")
        return
    
    # 设置参数
    image_folder = "矩阵产品"  # 图片文件夹
    output_video = "output_video_gpt.mp4"  # 输出视频文件名
    
    # 检查图片文件夹是否存在
    if not os.path.exists(image_folder):
        print(f"错误：文件夹 '{image_folder}' 不存在")
        print(f"当前工作目录：{os.getcwd()}")
        return
    
    # 初始化视频生成器
    generator = VideoGeneratorV3(gpt_api_key)
    
    try:
        # 1. 读取图片
        images = generator.read_images_from_folder(image_folder)
        if not images:
            print("错误：未找到任何图片")
            return
        
        # 2. 显示可用的语音选项
        print("\n可用的GPT TTS语音选项：")
        for category, voices in GPT_VOICE_OPTIONS.items():
            print(f"\n{category}:")
            for voice_id, description in voices.items():
                print(f"  - {voice_id}: {description}")
        
        # 3. 选择语音
        print("\n请选择语音（直接回车使用默认 'alloy'）：")
        voice_input = input().strip()
        voice = voice_input if voice_input else "alloy"
        
        # 4. 选择模型
        print("\n请选择模型（1: 标准质量 tts-1, 2: 高质量 tts-1-hd，直接回车使用标准）：")
        model_input = input().strip()
        model = "tts-1-hd" if model_input == "2" else "tts-1"
        
        # 5. 获取文案
        print("\n请输入要配音的文案（输入完成后按两次回车）：")
        lines = []
        empty_count = 0
        
        while empty_count < 2:
            line = input()
            if line:
                lines.append(line)
                empty_count = 0
            else:
                empty_count += 1
        
        text = '\n'.join(lines[:-1] if lines and lines[-1] == '' else lines)
        
        if not text.strip():
            print("错误：未输入文案")
            return
        
        print(f"\n输入的文案：\n{text}")
        print(f"选择的语音：{voice}")
        print(f"选择的模型：{model}")
        
        # 6. 生成语音和字幕
        result = generator.generate_speech_and_subtitles(text, voice, model)
        if not result:
            print("生成语音失败")
            return
        
        audio_path, subtitles = result
        
        # 7. 保存字幕文件（可选）
        srt_path = output_video.replace('.mp4', '.srt')
        generator.save_srt_file(subtitles, srt_path)
        
        # 8. 创建视频
        generator.create_video_with_subtitles(
            images=images,
            audio_path=audio_path,
            subtitles=subtitles,
            output_path=output_video,
            transition_duration=0.5
        )
        
        print(f"\n✅ 所有任务完成！")
        print(f"视频文件：{output_video}")
        print(f"字幕文件：{srt_path}")
        
    except Exception as e:
        print(f"\n❌ 发生错误：{e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        generator.cleanup_temp_files()

if __name__ == "__main__":
    main()
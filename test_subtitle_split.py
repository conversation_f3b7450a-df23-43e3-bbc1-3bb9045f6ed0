#!/usr/bin/env python3
"""
测试字幕分割功能
"""

from generate_video_from_images_v2 import VideoGeneratorV2
import os
from dotenv import load_dotenv

def test_subtitle_split():
    """测试字幕分割算法"""
    print("=== 测试字幕分割功能 ===\n")
    
    # 创建模拟的长字幕
    test_subtitles = [
        {
            'text': '这是一个非常长的句子，包含了很多内容，需要被分割成多个短字幕，这样观看体验会更好。',
            'begin_time': 0,
            'end_time': 10000  # 10秒
        },
        {
            'text': '第二个句子。这里有标点符号！可以按标点分割吗？应该可以的。',
            'begin_time': 10000,
            'end_time': 18000  # 8秒
        },
        {
            'text': '短句子',
            'begin_time': 18000,
            'end_time': 20000  # 2秒
        }
    ]
    
    # 初始化生成器（只用于测试分割功能）
    generator = VideoGeneratorV2("test", "test")
    
    print("原始字幕：")
    for i, sub in enumerate(test_subtitles):
        start = sub['begin_time'] / 1000
        end = sub['end_time'] / 1000
        duration = end - start
        print(f"{i+1}. [{start:.1f}s-{end:.1f}s] ({duration:.1f}s): {sub['text']}")
    
    print("\n分割后的字幕：")
    all_split_subtitles = []
    
    for subtitle in test_subtitles:
        split_subs = generator.split_long_subtitle(subtitle, max_duration=3.0, max_chars=20)
        all_split_subtitles.extend(split_subs)
    
    for i, sub in enumerate(all_split_subtitles):
        start = sub['begin_time'] / 1000
        end = sub['end_time'] / 1000
        duration = end - start
        print(f"{i+1}. [{start:.1f}s-{end:.1f}s] ({duration:.1f}s): {sub['text']}")
    
    print(f"\n总结：")
    print(f"原始字幕数量: {len(test_subtitles)} 条")
    print(f"分割后字幕数量: {len(all_split_subtitles)} 条")

def test_with_real_api():
    """使用真实API测试"""
    print("\n\n=== 使用真实API测试 ===\n")
    
    load_dotenv()
    app_id = os.getenv('VOLCANO_APP_ID')
    access_token = os.getenv('VOLCANO_ACCESS_TOKEN')
    
    if not app_id or not access_token:
        print("请配置API凭证")
        return
    
    generator = VideoGeneratorV2(app_id, access_token)
    
    # 测试文本
    test_text = """
    这是一段测试文本，包含了多个句子。第一个句子比较长，用来测试长句子的分割效果。
    第二个句子稍微短一些。第三个句子也不长。
    最后一个句子用来结束测试。
    """
    
    print("测试文本：")
    print(test_text.strip())
    
    # 生成语音和字幕
    result = generator.generate_speech_and_subtitles(test_text.strip())
    
    if result:
        audio_path, subtitles = result
        print(f"\n生成的字幕详情：")
        for i, sub in enumerate(subtitles):
            start = sub['begin_time'] / 1000
            end = sub['end_time'] / 1000
            duration = end - start
            print(f"{i+1}. [{start:.1f}s-{end:.1f}s] ({duration:.1f}s): {sub['text']}")
        
        # 清理临时文件
        generator.cleanup_temp_files()

if __name__ == "__main__":
    # 测试分割算法
    test_subtitle_split()
    
    # 可选：测试真实API
    response = input("\n是否测试真实API？(y/n): ")
    if response.lower() == 'y':
        test_with_real_api()
#!/usr/bin/env python3
"""
YouTube视频管理系统 - 命令行版本
不依赖PySide6，可以在GUI无法启动时使用
"""

import os
import sys
import json
import logging
import argparse
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database import Database
from youtube_database import YouTubeDatabase
from youtube_auth import YouTube<PERSON>uthManager
from youtube_uploader import YouTubeUploader
from video_manager import VideoManager
from upload_queue_manager import UploadQueueManager
from file_manager import FileManager

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class YouTubeManagerCLI:
    """YouTube管理系统命令行界面"""
    
    def __init__(self):
        # 初始化各个组件
        self.project_db = Database()
        self.youtube_db = YouTubeDatabase()
        self.file_manager = FileManager()
        self.auth_manager = YouTubeAuthManager()
        self.video_manager = VideoManager(self.project_db, self.youtube_db, self.file_manager)
        self.queue_manager = UploadQueueManager(self.youtube_db, self.auth_manager)
    
    def list_videos(self, status: str = None):
        """列出视频"""
        print("\n=== 视频列表 ===")
        
        if status:
            videos = self.youtube_db.get_videos_by_status(status)
            print(f"状态: {status}")
        else:
            videos = []
            for s in ['generated', 'ready', 'uploading', 'uploaded', 'failed']:
                videos.extend(self.youtube_db.get_videos_by_status(s))
            print("状态: 全部")
        
        if not videos:
            print("没有找到视频")
            return
        
        print(f"\n找到 {len(videos)} 个视频:")
        print("-" * 100)
        print(f"{'ID':>5} | {'文件名':<40} | {'状态':<10} | {'大小':<10} | {'创建时间':<20}")
        print("-" * 100)
        
        for video in videos:
            filename = os.path.basename(video['file_path'])[:40]
            size = f"{video.get('file_size', 0) / (1024*1024):.1f}MB"
            created = str(video['created_at'])[:19]
            
            print(f"{video['id']:>5} | {filename:<40} | {video['status']:<10} | {size:<10} | {created:<20}")
    
    def scan_videos(self):
        """扫描并导入视频"""
        print("\n=== 扫描视频 ===")
        print("正在扫描视频文件...")
        
        result = self.video_manager.auto_import_all_videos()
        
        print(f"\n扫描完成:")
        print(f"- 发现视频: {result['total_found']}")
        print(f"- 成功导入: {result['imported']}")
        print(f"- 失败: {result['failed']}")
        
        if result['imported_videos']:
            print("\n导入的视频:")
            for v in result['imported_videos']:
                print(f"  - ID: {v['video_id']}, 文件: {v['file_name']}")
        
        if result['failed_videos']:
            print("\n失败的视频:")
            for v in result['failed_videos']:
                print(f"  - {v['file_path']}: {v['error']}")
    
    def list_accounts(self):
        """列出YouTube账号"""
        print("\n=== YouTube账号列表 ===")
        
        accounts = self.youtube_db.get_active_youtube_accounts()
        
        if not accounts:
            print("没有找到账号")
            return
        
        print(f"\n找到 {len(accounts)} 个账号:")
        print("-" * 80)
        print(f"{'ID':>5} | {'账号名称':<20} | {'频道名':<30} | {'配额使用':<15}")
        print("-" * 80)
        
        for account in accounts:
            quota = f"{account.get('quota_used', 0)}/{account.get('quota_limit', 10000)}"
            print(f"{account['id']:>5} | {account['account_name']:<20} | "
                  f"{account.get('channel_name', 'N/A'):<30} | {quota:<15}")
    
    def add_account(self, name: str, client_secrets: str, email: str = None):
        """添加YouTube账号"""
        print(f"\n=== 添加YouTube账号: {name} ===")
        
        if not os.path.exists(client_secrets):
            print(f"错误: 客户端密钥文件不存在: {client_secrets}")
            return
        
        try:
            # 认证账号
            print("正在进行OAuth认证，请在浏览器中授权...")
            auth_info = self.auth_manager.authenticate_account(name, client_secrets)
            
            # 保存到数据库
            account_id = self.youtube_db.add_youtube_account(
                account_name=auth_info['account_name'],
                email=email,
                credentials_path=auth_info['credentials_path'],
                channel_id=auth_info['channel_id'],
                channel_name=auth_info['channel_name']
            )
            
            print(f"\n✅ 账号添加成功!")
            print(f"- 账号ID: {account_id}")
            print(f"- 频道名: {auth_info['channel_name']}")
            print(f"- 订阅数: {auth_info['subscriber_count']}")
            
        except Exception as e:
            print(f"\n❌ 添加账号失败: {str(e)}")
    
    def create_upload_tasks(self, video_ids: List[int], account_ids: List[int],
                           interval: int = 10, daily_limit: int = 5):
        """创建上传任务"""
        print(f"\n=== 创建上传任务 ===")
        print(f"- 视频数量: {len(video_ids)}")
        print(f"- 账号数量: {len(account_ids)}")
        print(f"- 发布间隔: {interval}分钟")
        print(f"- 每日限制: {daily_limit}个/账号")
        
        schedule_config = {
            'interval_minutes': interval,
            'daily_limit': daily_limit
        }
        
        task_ids = self.queue_manager.add_batch_upload_tasks(
            video_ids, account_ids, schedule_config
        )
        
        print(f"\n✅ 成功创建 {len(task_ids)} 个上传任务")
    
    def list_tasks(self, status: str = None):
        """列出上传任务"""
        print("\n=== 上传任务列表 ===")
        
        tasks = self.youtube_db.get_pending_upload_tasks(limit=1000)
        
        if status:
            tasks = [t for t in tasks if t['status'] == status]
            print(f"状态: {status}")
        else:
            print("状态: 全部待处理")
        
        if not tasks:
            print("没有找到任务")
            return
        
        print(f"\n找到 {len(tasks)} 个任务:")
        print("-" * 100)
        print(f"{'ID':>5} | {'视频ID':>8} | {'账号ID':>8} | {'状态':<10} | "
              f"{'重试次数':>8} | {'计划时间':<20}")
        print("-" * 100)
        
        for task in tasks:
            scheduled = str(task.get('scheduled_time', 'N/A'))[:19]
            print(f"{task['id']:>5} | {task['video_id']:>8} | {task['account_id']:>8} | "
                  f"{task['status']:<10} | {task['retry_count']:>8} | {scheduled:<20}")
    
    def start_queue(self):
        """启动上传队列"""
        print("\n=== 启动上传队列 ===")
        self.queue_manager.start()
        print("✅ 队列已启动")
        print("提示: 使用 Ctrl+C 停止队列")
        
        try:
            # 保持运行
            import time
            while True:
                time.sleep(30)
                status = self.queue_manager.get_queue_status()
                print(f"\r队列状态: 活跃任务={status['active_tasks']}, "
                      f"已处理={status['processed']}, "
                      f"成功={status['successful']}, "
                      f"失败={status['failed']}", end='')
        except KeyboardInterrupt:
            print("\n\n正在停止队列...")
            self.queue_manager.stop()
            print("✅ 队列已停止")
    
    def show_statistics(self):
        """显示统计信息"""
        print("\n=== 系统统计 ===")
        
        stats = self.youtube_db.get_upload_statistics()
        
        print(f"\n视频统计:")
        print(f"- 总数: {stats['total_videos']}")
        for status, count in stats['video_status_counts'].items():
            print(f"  - {status}: {count}")
        
        print(f"\n任务统计:")
        print(f"- 总数: {stats['total_tasks']}")
        for status, count in stats['task_status_counts'].items():
            print(f"  - {status}: {count}")
        
        print(f"\n其他:")
        print(f"- 活跃账号: {stats['active_accounts']}")
        print(f"- 今日上传: {stats['today_uploads']}")
    
    def interactive_mode(self):
        """交互模式"""
        print("\nYouTube视频管理系统 - 命令行界面")
        print("=" * 50)
        
        while True:
            print("\n可用命令:")
            print("1. 列出视频 (lv)")
            print("2. 扫描视频 (sv)")
            print("3. 列出账号 (la)")
            print("4. 添加账号 (aa)")
            print("5. 创建上传任务 (ct)")
            print("6. 列出任务 (lt)")
            print("7. 启动队列 (sq)")
            print("8. 显示统计 (st)")
            print("9. 退出 (q)")
            
            cmd = input("\n请输入命令: ").strip().lower()
            
            if cmd in ['1', 'lv']:
                status = input("输入状态过滤(留空显示全部): ").strip() or None
                self.list_videos(status)
            
            elif cmd in ['2', 'sv']:
                self.scan_videos()
            
            elif cmd in ['3', 'la']:
                self.list_accounts()
            
            elif cmd in ['4', 'aa']:
                name = input("账号名称: ").strip()
                client_secrets = input("客户端密钥文件路径: ").strip()
                email = input("邮箱(可选): ").strip() or None
                if name and client_secrets:
                    self.add_account(name, client_secrets, email)
                else:
                    print("账号名称和客户端密钥文件不能为空")
            
            elif cmd in ['5', 'ct']:
                # 先列出可用的视频和账号
                self.list_videos('ready')
                video_ids_str = input("\n输入视频ID(逗号分隔): ").strip()
                
                self.list_accounts()
                account_ids_str = input("\n输入账号ID(逗号分隔): ").strip()
                
                if video_ids_str and account_ids_str:
                    try:
                        video_ids = [int(x.strip()) for x in video_ids_str.split(',')]
                        account_ids = [int(x.strip()) for x in account_ids_str.split(',')]
                        interval = int(input("发布间隔(分钟)[10]: ").strip() or "10")
                        daily_limit = int(input("每日限制(个/账号)[5]: ").strip() or "5")
                        
                        self.create_upload_tasks(video_ids, account_ids, interval, daily_limit)
                    except ValueError:
                        print("输入格式错误")
            
            elif cmd in ['6', 'lt']:
                status = input("输入状态过滤(留空显示全部): ").strip() or None
                self.list_tasks(status)
            
            elif cmd in ['7', 'sq']:
                self.start_queue()
            
            elif cmd in ['8', 'st']:
                self.show_statistics()
            
            elif cmd in ['9', 'q']:
                print("再见!")
                break
            
            else:
                print("无效命令")


def main():
    parser = argparse.ArgumentParser(description='YouTube视频管理系统 - 命令行界面')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 列出视频
    parser_lv = subparsers.add_parser('list-videos', help='列出视频')
    parser_lv.add_argument('--status', help='状态过滤')
    
    # 扫描视频
    parser_sv = subparsers.add_parser('scan-videos', help='扫描并导入视频')
    
    # 列出账号
    parser_la = subparsers.add_parser('list-accounts', help='列出YouTube账号')
    
    # 添加账号
    parser_aa = subparsers.add_parser('add-account', help='添加YouTube账号')
    parser_aa.add_argument('name', help='账号名称')
    parser_aa.add_argument('client_secrets', help='客户端密钥文件路径')
    parser_aa.add_argument('--email', help='邮箱地址')
    
    # 创建任务
    parser_ct = subparsers.add_parser('create-tasks', help='创建上传任务')
    parser_ct.add_argument('--videos', required=True, help='视频ID列表(逗号分隔)')
    parser_ct.add_argument('--accounts', required=True, help='账号ID列表(逗号分隔)')
    parser_ct.add_argument('--interval', type=int, default=10, help='发布间隔(分钟)')
    parser_ct.add_argument('--daily-limit', type=int, default=5, help='每日限制')
    
    # 列出任务
    parser_lt = subparsers.add_parser('list-tasks', help='列出上传任务')
    parser_lt.add_argument('--status', help='状态过滤')
    
    # 启动队列
    parser_sq = subparsers.add_parser('start-queue', help='启动上传队列')
    
    # 显示统计
    parser_st = subparsers.add_parser('stats', help='显示统计信息')
    
    args = parser.parse_args()
    
    # 创建CLI实例
    cli = YouTubeManagerCLI()
    
    # 执行命令
    if args.command == 'list-videos':
        cli.list_videos(args.status)
    elif args.command == 'scan-videos':
        cli.scan_videos()
    elif args.command == 'list-accounts':
        cli.list_accounts()
    elif args.command == 'add-account':
        cli.add_account(args.name, args.client_secrets, args.email)
    elif args.command == 'create-tasks':
        video_ids = [int(x.strip()) for x in args.videos.split(',')]
        account_ids = [int(x.strip()) for x in args.accounts.split(',')]
        cli.create_upload_tasks(video_ids, account_ids, args.interval, args.daily_limit)
    elif args.command == 'list-tasks':
        cli.list_tasks(args.status)
    elif args.command == 'start-queue':
        cli.start_queue()
    elif args.command == 'stats':
        cli.show_statistics()
    else:
        # 如果没有指定命令，进入交互模式
        cli.interactive_mode()


if __name__ == '__main__':
    main()
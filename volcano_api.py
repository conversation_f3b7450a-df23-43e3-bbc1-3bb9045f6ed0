import requests
import time
import os
import uuid
import tempfile
import json
import asyncio
import aiohttp
import ssl
import re
from typing import Dict, Optional, BinaryIO, List

class VolcanoSpeechAPI:
    def __init__(self, app_id: str, access_token: str):
        self.app_id = app_id
        self.access_token = access_token
        self.base_url = "https://openspeech.bytedance.com/api/v1/vc"
        self.tts_base_url = "https://openspeech.bytedance.com/api/v1/tts_async"
        
    def submit_audio(self, audio_file: BinaryIO, 
                    words_per_line: int = 20,
                    max_lines: int = 2,
                    language: str = "zh-CN",
                    use_punc: bool = True,
                    caption_type: str = "speech") -> Dict:
        """提交音频文件进行转录"""
        
        url = f"{self.base_url}/submit"
        params = {
            "appid": self.app_id,
            "words_per_line": words_per_line,
            "max_lines": max_lines,
            "language": language,
            "use_punc": use_punc,
            "caption_type": caption_type,
            "use_itn": True
        }
        
        headers = {
            "Content-Type": "audio/wav",
            "Authorization": f"Bearer; {self.access_token}"
        }
        
        try:
            response = requests.post(
                url, 
                params=params,
                headers=headers,
                data=audio_file.read()
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {
                "code": -1,
                "message": f"请求失败: {str(e)}"
            }
    
    def query_result(self, task_id: str, blocking: bool = True) -> Dict:
        """查询转录结果"""
        
        url = f"{self.base_url}/query"
        params = {
            "appid": self.app_id,
            "id": task_id,
            "blocking": 1 if blocking else 0
        }
        
        headers = {
            "Authorization": f"Bearer; {self.access_token}"
        }
        
        try:
            response = requests.get(
                url,
                params=params,
                headers=headers
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {
                "code": -1,
                "message": f"查询失败: {str(e)}"
            }
    
    def wait_for_result(self, task_id: str, timeout: int = 300) -> Dict:
        """等待并获取转录结果"""
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            result = self.query_result(task_id, blocking=False)
            
            if result.get("code") == 0:
                return result
            elif result.get("code") == 2000:
                time.sleep(2)
                continue
            else:
                return result
        
        return {
            "code": -1,
            "message": "处理超时"
        }
        
    def create_tts_task(self, 
                       text: str,
                       voice_type: str = "BV701_streaming",
                       format: str = "mp3",
                       sample_rate: int = 24000,
                       volume: float = 1.0,
                       speed: float = 1.0,
                       pitch: float = 1.0,
                       enable_subtitle: int = 1,
                       sentence_interval: int = 0,
                       style: Optional[str] = None,
                       callback_url: Optional[str] = None) -> Dict:
        """创建TTS（文本转语音）任务"""
        
        url = f"{self.tts_base_url}/submit"
        
        payload = {
            "appid": self.app_id,
            "reqid": str(uuid.uuid4()),
            "text": text,
            "format": format,
            "voice_type": voice_type,
            "sample_rate": sample_rate,
            "volume": volume,
            "speed": speed,
            "pitch": pitch,
            "enable_subtitle": enable_subtitle,
            "sentence_interval": sentence_interval
        }
        
        if style:
            payload["style"] = style
        if callback_url:
            payload["callback_url"] = callback_url
            
        headers = {
            "Content-Type": "application/json",
            "Resource-Id": 'volc.tts_async.default',
            "Authorization": f"Bearer; {self.access_token}"
        }
        
        try:
            print(f"请求URL: {url}")
            print(f"请求Headers: {headers}")
            print(f"AppID: {self.app_id}")
            
            response = requests.post(url, json=payload, headers=headers)
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"响应内容: {response.text}")
            
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求异常详情: {e}")
            return {
                "code": -1,
                "message": f"请求失败: {str(e)}"
            }
    
    def query_tts_result(self, task_id: str) -> Dict:
        """查询TTS任务结果"""
        
        url = f"{self.tts_base_url}/query"
        params = {
            "appid": self.app_id,
            "task_id": task_id
        }
        
        headers = {
            "Resource-Id": "volc.tts_async.default",  # 与创建任务保持一致
            "Authorization": f"Bearer; {self.access_token}"
        }
        
        try:
            full_url = f"{url}?appid={params['appid']}&task_id={params['task_id']}"
            print(f"  查询URL: {full_url}")
            
            response = requests.get(url, params=params, headers=headers)
            
            if response.status_code != 200:
                print(f"  查询响应状态码: {response.status_code}")
                print(f"  查询响应内容: {response.text}")
            
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"  查询异常: {e}")
            return {
                "code": -1,
                "message": f"查询失败: {str(e)}"
            }
    
    def wait_for_tts_result(self, task_id: str, timeout: int = 10800) -> Dict:
        """等待TTS任务完成（最长3小时）"""
        
        start_time = time.time()
        check_count = 0
        
        while time.time() - start_time < timeout:
            check_count += 1
            elapsed = int(time.time() - start_time)
            
            if check_count > 1:
                wait_time = min(30, 5 * (check_count // 10 + 1))
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            
            print(f"检查任务状态 (第{check_count}次，已等待{elapsed}秒)...")
            result = self.query_tts_result(task_id)
            
            print(f"  查询结果: {json.dumps(result, ensure_ascii=False)}")
            
            # 检查是否有错误
            if result.get("code") and result.get("code") != 0:
                print(f"  查询出错: {result.get('message')}")
                return result
            
            if "task_status" in result:
                status = result["task_status"]
                print(f"  任务状态码: {status} (0=处理中, 1=成功, 2=失败)")
                
                if status == 1:  # 成功
                    print("  ✅ 任务成功!")
                    return result
                elif status == 2:  # 失败
                    print("  ❌ 任务失败!")
                    return result
                elif status == 0:  # 处理中
                    print("  ⏳ 任务仍在处理中...")
        
        return {
            "code": -1,
            "message": "等待超时"
        }
    
    def text_to_speech_complete(self, text: str, **kwargs) -> Optional[Dict]:
        """完整的文本转语音流程，返回包含音频文件路径和TTS结果数据的字典"""
        
        print(f"\n{'='*50}")
        print(f"火山引擎TTS任务")
        print(f"文本长度: {len(text)} 字符")
        print(f"音色: {kwargs.get('voice_type', 'BV701_streaming')}")
        print(f"{'='*50}\n")
        
        # 创建任务
        print("1. 创建TTS任务...")
        create_result = self.create_tts_task(text, **kwargs)
        
        if "code" in create_result and create_result["code"] != 0:
            print(f"❌ 创建任务失败: {create_result.get('message')}")
            return None
            
        task_id = create_result.get("task_id")
        if not task_id:
            print("❌ 未获取到task_id")
            return None
            
        print(f"✅ 任务创建成功，task_id: {task_id}")
        print(f"预计消耗字符数: {create_result.get('text_length', 0)}")
        
        # 等待任务完成
        print("\n2. 等待任务完成（可能需要几分钟到几十分钟）...")
        result = self.wait_for_tts_result(task_id)
        
        if result.get("task_status") != 1:
            print(f"❌ 任务失败: {result.get('message', '未知错误')}")
            return None
            
        audio_url = result.get("audio_url")
        if not audio_url:
            print("❌ 未获取到音频URL")
            return None
            
        print(f"✅ 任务完成!")
        print(f"音频URL有效期至: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(result.get('url_expire_time', 0)))}")
        
        # 下载音频
        print("\n3. 下载音频文件...")
        try:
            response = requests.get(audio_url, stream=True)
            response.raise_for_status()
            
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
            temp_path = temp_file.name
            
            with open(temp_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
                    
            file_size = os.path.getsize(temp_path) / 1024 / 1024
            print(f"✅ 音频下载成功: {temp_path}")
            print(f"文件大小: {file_size:.2f} MB")
            
            # 返回音频路径和TTS结果数据
            return {
                "audio_path": temp_path,
                "tts_result": result
            }
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            return None

def format_time_for_srt(milliseconds: int) -> str:
    """将毫秒转换为SRT时间格式"""
    total_seconds = milliseconds / 1000
    hours = int(total_seconds // 3600)
    minutes = int((total_seconds % 3600) // 60)
    seconds = int(total_seconds % 60)
    ms = int((total_seconds % 1) * 1000)
    
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{ms:03d}"

def convert_to_srt(utterances: list) -> str:
    """将火山引擎的结果转换为SRT格式"""
    srt_content = []
    
    for i, utterance in enumerate(utterances, 1):
        start_time = format_time_for_srt(utterance['start_time'])
        end_time = format_time_for_srt(utterance['end_time'])
        text = utterance['text'].strip()
        
        if text:
            srt_content.append(f"{i}")
            srt_content.append(f"{start_time} --> {end_time}")
            srt_content.append(text)
            srt_content.append("")
    
    return '\n'.join(srt_content)

def convert_tts_to_srt(sentences: list) -> str:
    """将TTS返回的sentences转换为SRT格式字幕"""
    srt_content = []
    
    for i, sentence in enumerate(sentences):
        # 序号
        srt_content.append(str(i + 1))
        
        # 时间轴
        start_time = format_time_for_srt(sentence.get("begin_time", 0))
        end_time = format_time_for_srt(sentence.get("end_time", 0))
        srt_content.append(f"{start_time} --> {end_time}")
        
        # 文本内容
        text = sentence.get("text", "")
        srt_content.append(text)
        srt_content.append("")
    
    return '\n'.join(srt_content)

# 火山引擎TTS音色列表
VOLCANO_VOICE_OPTIONS = {
    "通用女声": {
        "BV001_streaming": "通用女声",
        "BV002": "通用女声-清新",
        "BV701_streaming": "专业女声-温暖",
        "BV702_streaming": "专业女声-知性"
    },
    "通用男声": {
        "BV003_streaming": "通用男声",
        "BV004": "通用男声-活力", 
        "BV703_streaming": "专业男声-沉稳",
        "BV704_streaming": "专业男声-青年"
    },
    "童声": {
        "BV705_streaming": "儿童音-男孩",
        "BV706_streaming": "儿童音-女孩"
    },
    "方言": {
        "BV213_streaming": "粤语女声",
        "BV214_streaming": "粤语男声"
    }
}

def clean_and_parse_json(response_text: str) -> Optional[List[Dict]]:
    """
    清理并解析可能包含markdown格式的JSON响应

    支持的格式:
    1. 带前缀文本和 'json' 标记的响应
    2. 带 ```json 代码块的响应
    3. 纯JSON格式
    4. 包含其他文本但含有有效JSON的响应

    返回:
        List[Dict]: 解析后的场景列表，每个场景包含 start, end, img_prompt 字段
        None: 解析失败或数据验证失败
    """
    if not response_text or not response_text.strip():
        print("输入文本为空")
        return None

    try:
        # 移除可能的前缀文本（如"成功获取AI分镜提示词:"）
        lines = response_text.strip().split('\n')

        # 查找JSON开始的位置
        json_start = -1
        for i, line in enumerate(lines):
            stripped_line = line.strip()
            if stripped_line.startswith('[') or stripped_line == 'json' or stripped_line.startswith('```json'):
                if stripped_line == 'json' or stripped_line.startswith('```json'):
                    # 如果是markdown标记，JSON在下一行开始
                    json_start = i + 1
                else:
                    # 如果直接是JSON，从当前行开始
                    json_start = i
                break

        if json_start == -1:
            # 没找到明确的JSON开始标记，尝试查找第一个'['
            for i, line in enumerate(lines):
                if '[' in line:
                    json_start = i
                    break

        if json_start == -1:
            print("未找到JSON开始标记")
            return None

        # 从JSON开始位置提取内容
        json_lines = lines[json_start:]

        # 移除可能的结束标记（如```）
        cleaned_lines = []
        for line in json_lines:
            stripped_line = line.strip()
            if stripped_line.startswith('```') and len(stripped_line) <= 10:
                # 遇到结束标记，停止
                break
            cleaned_lines.append(line)

        # 重新组合JSON文本
        json_text = '\n'.join(cleaned_lines).strip()

        # 如果文本不是以[开始，尝试找到第一个[
        if not json_text.startswith('['):
            bracket_pos = json_text.find('[')
            if bracket_pos != -1:
                json_text = json_text[bracket_pos:]
            else:
                print("未找到JSON数组开始标记")
                return None

        # 如果文本不是以]结束，尝试找到最后一个]
        if not json_text.endswith(']'):
            bracket_pos = json_text.rfind(']')
            if bracket_pos != -1:
                json_text = json_text[:bracket_pos + 1]
            else:
                print("未找到JSON数组结束标记")
                return None

        # 解析JSON
        parsed_data = json.loads(json_text)

        # 验证数据格式
        if not isinstance(parsed_data, list):
            print("解析的数据不是数组格式")
            return None

        # 验证每个元素是否包含必要字段
        for i, item in enumerate(parsed_data):
            if not isinstance(item, dict):
                print(f"数组元素 {i+1} 不是字典格式")
                return None
            if not all(key in item for key in ['start', 'end', 'img_prompt']):
                print(f"数组元素 {i+1} 缺少必要字段: {item}")
                return None

            # 验证数据类型
            if not isinstance(item['start'], (int, float)) or not isinstance(item['end'], (int, float)):
                print(f"数组元素 {i+1} 的时间字段不是数字类型")
                return None
            if not isinstance(item['img_prompt'], str) or not item['img_prompt'].strip():
                print(f"数组元素 {i+1} 的提示词字段无效")
                return None

            # 验证时间逻辑
            if item['start'] >= item['end']:
                print(f"数组元素 {i+1} 的开始时间不能大于等于结束时间")
                return None

        print(f"成功解析JSON，包含{len(parsed_data)}个场景")
        return parsed_data

    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        print(f"尝试解析的文本: {json_text[:200]}...")
        return None
    except Exception as e:
        print(f"清理JSON时发生错误: {e}")
        return None

async def get_ai_prompt_from_subtitles(subtitles: List[Dict], api_key: str = None, custom_prompt: str = None) -> Optional[List[Dict]]:
    """根据字幕生成AI分镜提示词，返回解析后的JSON数据

    Args:
        subtitles: 字幕数据列表
        api_key: API密钥
        custom_prompt: 自定义提示词模板，如果为None则使用默认模板
    """

    MAX_RETRIES = 3
    API_KEY = api_key or os.getenv('VOLCANO_API_KEY', 'd5507c70-cadc-4d21-873c-56e26cbc7fcd')
    API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions'
    
    # 将字幕格式化为输入格式
    subtitle_content = json.dumps(subtitles, ensure_ascii=False, indent=2)
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }
    
    # 使用自定义提示词或默认提示词
    if custom_prompt:
        # 如果用户提供了自定义提示词，在末尾添加字幕数据
        prompt = f"""
        你是一名专业的“分镜设计师”AI 助手，专精于将短视频字幕转换为 AI 分镜提示词。
        
        {custom_prompt}
---

**语言与格式要求**  
- 每个 **img_prompt** 为一条紧凑英文单句，逗号连接短语，不使用冗余连接词或抽象形容。  
- 输出 JSON 结构严格遵循示例格式,不要解释，必须返回严格的json 格式：  

json
[
  {{
    "start": 1500000,
    "end": 2100000,
    "img_prompt": "a sleek humanoid AI assistant unveiling a glowing server core, gesture graceful and confident, eyes glowing with certainty, dominates the foreground, dynamic isometric perspective, inside a neon-lit data center, intense cyan rim light and subtle magenta backglow, sleek neon-tech illustration style, sharp vector lines, cyan-magenta glow, cinematic rim lighting, high-end product advertisement look"
  }}
]
以下是需要处理的原始字幕：

{subtitle_content}"""
    else:
        # 使用默认的未来科幻风格提示词
        prompt = f"""你是一名专业的“分镜设计师”AI 助手，专精于将短视频字幕转换为具有**未来科幻宣传 / 智能体代言风格**的 AI 分镜提示词。你所生成的画面需兼具商业广告的科技质感与产品海报的冲击力：色调以青蓝-品红霓虹为主，高对比炫光、玻璃质感与发光线条勾勒出智能体与尖端硬件；整体气氛冷峻、干净、极具未来感。

---

**输入**  
你将收到一个按时间排序的文本，每个元素包含：  
- **content** : 字幕文本  
- **start**  : 起始时间（毫秒）  
- **end**    : 结束时间（毫秒）  

---

**输出**  
先根据字幕信息与情绪，将相邻 2-5 句合并为同一视觉段落，重新划分图像节奏。然后输出一个 **JSON 数组**，每个元素为一个镜头段落的描述对象，字段如下：  
- **start** : 合并后镜头段落的起始时间（毫秒）  
- **end**   : 合并后镜头段落的结束时间（毫秒）  
- **img_prompt** : 针对该镜头段落生成的 AI 图像提示词（英文）  

---

### img_prompt 描述要求（英文单句，逗号分隔）
每条 **img_prompt** 必须依次或交错包含以下 8 个描写要素（短语之间用逗号，顺序灵活）：  

1. **角色身份** (e.g. *a sleek humanoid AI assistant*, *a confident tech evangelist*)  
2. **姿态与动作** (e.g. *presenting a floating data cube*, *pointing at a holographic server rack*)  
3. **情绪表现** (e.g. *eyes glowing with certainty*, *subtle triumphant smile*)  
4. **构图位置** (e.g. *dominates the foreground*, *centered full-body view*)  
5. **镜头视角** (e.g. *dramatic low-angle shot*, *dynamic isometric perspective*)  
6. **环境背景** (e.g. *inside a neon-lit data center*, *hovering above a hologram cityscape*)  
7. **光影渲染** (e.g. *intense cyan rim light*, *diffuse magenta backglow*)  
8. **统一风格锚定（必须同时包含下列短语）**  
   - sleek neon-tech illustration style  
   - sharp vector lines  
   - cyan-magenta glow  
   - cinematic rim lighting  
   - high-end product advertisement look  

---

**语言与格式要求**  
- 每个 **img_prompt** 为一条紧凑英文单句，逗号连接短语，不使用冗余连接词或抽象形容。  
- 输出 JSON 结构严格遵循示例格式,不要解释，必须返回严格的json 格式：  

json
[
  {{
    "start": 1500000,
    "end": 2100000,
    "img_prompt": "a sleek humanoid AI assistant unveiling a glowing server core, gesture graceful and confident, eyes glowing with certainty, dominates the foreground, dynamic isometric perspective, inside a neon-lit data center, intense cyan rim light and subtle magenta backglow, sleek neon-tech illustration style, sharp vector lines, cyan-magenta glow, cinematic rim lighting, high-end product advertisement look"
  }}
]
以下是需要处理的原始字幕：

{subtitle_content}"""
    
    payload = {
        "model": "ep-20250206185713-2kspx",
        "messages": [
            {"role": "system", "content": "你是一个专业提示词生成助手。"},
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 5000
    }
    
    async with aiohttp.ClientSession() as session:
        retries = 0
        while retries < MAX_RETRIES:
            try:
                print(f"开始生成AI分镜提示词（第{retries + 1}次尝试）")
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                
                async with session.post(API_URL, headers=headers, json=payload, ssl=ssl_context) as response:
                    if response.status != 200:
                        print(f"API请求失败，状态码: {response.status}")
                        error_text = await response.text()
                        print(f"错误详情: {error_text}")
                        retries += 1
                        await asyncio.sleep(1)
                        continue
                    
                    response_data = await response.json()
                    if 'error' in response_data:
                        print(f"API返回错误: {response_data['error']}")
                        retries += 1
                        await asyncio.sleep(1)
                        continue
                    if 'choices' not in response_data or not response_data['choices']:
                        print(f"API响应格式错误或choices为空: {response_data}")
                        retries += 1
                        await asyncio.sleep(1)
                        continue
                    
                    content = response_data['choices'][0]['message']['content']
                    print(f"成功获取AI分镜提示词原始响应: {content}")

                    # 清理并解析JSON
                    parsed_scenes = clean_and_parse_json(content)
                    if parsed_scenes:
                        print(f"成功解析{len(parsed_scenes)}个场景")
                        return parsed_scenes
                    else:
                        print("JSON解析失败，尝试下一次重试")
                        retries += 1
                        await asyncio.sleep(1)
                        continue
            except aiohttp.ClientConnectorCertificateError as e:
                print(f"SSL证书验证错误: {str(e)}")
                return None
            except Exception as e:
                print(f"请求发生错误: {type(e).__name__} - {str(e)}")
                retries += 1
                await asyncio.sleep(1)
        
        print(f"请求生成AI分镜提示词失败，已达到最大重试次数 {MAX_RETRIES}")
        return None

async def generate_image_from_prompt(prompt: str, api_key: str = None, model: str = "flux-schnell", size: str = "1024x1024") -> Optional[Dict]:
    """使用DMXAPI生成图片"""

    API_KEY = api_key or "sk-*****************************************************"
    API_HOST = "www.dmxapi.cn"
    API_ENDPOINT = "/v1/images/generations"

    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Accept": "application/json",
        "User-Agent": "DMXAPI/1.0.0 (https://www.dmxapi.cn)",
        "Content-Type": "application/json",
    }

    payload = {
        "prompt": prompt,
        "n": 1,
        "model": model,
        "size": size,
    }

    try:
        # 创建忽略SSL证书验证的连接器
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        connector = aiohttp.TCPConnector(ssl=ssl_context)
        async with aiohttp.ClientSession(connector=connector) as session:
            url = f"https://{API_HOST}{API_ENDPOINT}"
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    print(f"DMXAPI请求失败，状态码: {response.status}, 错误: {error_text}")
                    return None

                result = await response.json()
                print(f"成功生成图片")
                return result
    except Exception as e:
        print(f"生成图片时发生错误: {type(e).__name__} - {str(e)}")
        return None

async def generate_images_for_scenes(scenes: List[Dict], api_key: str = None, model: str = "flux-schnell") -> List[Dict]:
    """为所有场景生成图片"""
    
    results = []
    
    for i, scene in enumerate(scenes):
        print(f"\n正在为场景 {i+1} 生成图片...")
        print(f"时间段: {scene['start']/1000:.1f}s - {scene['end']/1000:.1f}s")
        
        # 生成图片
        image_result = await generate_image_from_prompt(
            prompt=scene['img_prompt'],
            api_key=api_key,
            model=model
        )
        
        if image_result and 'data' in image_result and len(image_result['data']) > 0:
            image_url = image_result['data'][0].get('url', '')
            
            # 下载图片
            if image_url:
                try:
                    # 创建忽略SSL证书验证的连接器
                    ssl_context = ssl.create_default_context()
                    ssl_context.check_hostname = False
                    ssl_context.verify_mode = ssl.CERT_NONE

                    connector = aiohttp.TCPConnector(ssl=ssl_context)
                    async with aiohttp.ClientSession(connector=connector) as session:
                        async with session.get(image_url) as resp:
                            if resp.status == 200:
                                image_data = await resp.read()

                                # 保存图片到临时文件
                                temp_file = tempfile.NamedTemporaryFile(
                                    delete=False,
                                    suffix='.png',
                                    prefix=f"scene_{i+1}_"
                                )
                                temp_file.write(image_data)
                                temp_file.close()

                                results.append({
                                    "scene_index": i + 1,
                                    "start": scene['start'],
                                    "end": scene['end'],
                                    "prompt": scene['img_prompt'],
                                    "image_url": image_url,
                                    "local_path": temp_file.name,
                                    "success": True
                                })
                                print(f"场景 {i+1} 图片生成成功并保存到: {temp_file.name}")
                            else:
                                results.append({
                                    "scene_index": i + 1,
                                    "start": scene['start'],
                                    "end": scene['end'],
                                    "prompt": scene['img_prompt'],
                                    "success": False,
                                    "error": f"下载图片失败: {resp.status}"
                                })
                except Exception as e:
                    results.append({
                        "scene_index": i + 1,
                        "start": scene['start'],
                        "end": scene['end'],
                        "prompt": scene['img_prompt'],
                        "success": False,
                        "error": f"下载图片时出错: {str(e)}"
                    })
        else:
            results.append({
                "scene_index": i + 1,
                "start": scene['start'],
                "end": scene['end'],
                "prompt": scene['img_prompt'],
                "success": False,
                "error": "API未返回图片URL"
            })
        
        # 避免请求过快
        await asyncio.sleep(1)
    
    return results
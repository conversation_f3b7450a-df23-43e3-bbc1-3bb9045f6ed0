import http.client
import json
import tempfile
import os
from typing import Optional, Dict

class GPTTextToSpeechAPI:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.host = "api.gptnb.ai"
        self.endpoint = "/v1/audio/speech"
        
    def text_to_speech(self, 
                      text: str,
                      voice: str = "alloy",
                      model: str = "tts-1",
                      response_format: str = "mp3") -> Optional[Dict]:
        """
        使用GPT API进行文本转语音
        
        Args:
            text: 要转换的文本
            voice: 语音类型 (alloy, echo, fable, onyx, nova, shimmer)
            model: 模型类型 (tts-1 或 tts-1-hd)
            response_format: 输出格式 (mp3, opus, aac, flac)
            
        Returns:
            包含音频文件路径的字典，失败时返回None
        """
        
        print(f"\n{'='*50}")
        print(f"GPT TTS 任务")
        print(f"文本长度: {len(text)} 字符")
        print(f"音色: {voice}")
        print(f"模型: {model}")
        print(f"格式: {response_format}")
        print(f"{'='*50}\n")
        
        try:
            print("1. 创建HTTPS连接...")
            conn = http.client.HTTPSConnection(self.host)
            
            payload = json.dumps({
                "model": model,
                "input": text,
                "voice": voice,
                "response_format": response_format
            })
            
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.api_key}'
            }
            
            print("2. 发送请求到GPT API...")
            conn.request("POST", self.endpoint, payload, headers)
            res = conn.getresponse()
            data = res.read()
            
            if res.status == 200:
                print("✅ 请求成功，保存音频文件...")
                
                # 创建临时文件保存音频
                suffix = f".{response_format}"
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
                temp_path = temp_file.name
                
                with open(temp_path, "wb") as f:
                    f.write(data)
                
                file_size = os.path.getsize(temp_path) / 1024 / 1024
                print(f"✅ 音频生成成功: {temp_path}")
                print(f"文件大小: {file_size:.2f} MB")
                
                conn.close()
                
                return {
                    "audio_path": temp_path,
                    "status": "success",
                    "file_size_mb": file_size,
                    "format": response_format,
                    "voice": voice,
                    "model": model
                }
            else:
                error_msg = data.decode('utf-8')
                print(f"❌ 请求失败，状态码: {res.status}")
                print(f"错误信息: {error_msg}")
                conn.close()
                return None
                
        except Exception as e:
            print(f"❌ 发生错误: {type(e).__name__} - {str(e)}")
            return None
    
    def text_to_speech_with_subtitles(self,
                                    text: str,
                                    voice: str = "alloy",
                                    model: str = "tts-1",
                                    response_format: str = "mp3") -> Optional[Dict]:
        """
        生成音频并创建简单的字幕（基于估算）
        
        注意：GPT TTS API不提供精确的时间戳，这里使用估算方法
        """
        
        # 首先生成音频
        result = self.text_to_speech(text, voice, model, response_format)
        
        if not result:
            return None
            
        # 估算字幕时间（这是一个简化的方法）
        # 假设平均语速为每分钟150-180个单词（英文）或每秒3-4个字符（中文）
        print("\n3. 生成估算字幕...")
        
        # 简单的句子分割
        sentences = self._split_text_to_sentences(text)
        
        # 估算总时长（基于字符数和平均语速）
        # 英文大约每个字符0.06秒，中文大约每个字符0.25秒
        is_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
        char_duration = 0.25 if is_chinese else 0.06
        
        # 根据模型调整语速
        if model == "tts-1-hd":
            char_duration *= 1.1  # HD模型可能稍慢
            
        # 根据语音类型微调
        voice_speed_factors = {
            "alloy": 1.0,
            "echo": 1.05,
            "fable": 0.95,
            "onyx": 1.1,
            "nova": 0.9,
            "shimmer": 1.0
        }
        char_duration *= voice_speed_factors.get(voice, 1.0)
        
        # 生成字幕时间戳
        subtitles = []
        current_time = 0
        
        for i, sentence in enumerate(sentences):
            sentence = sentence.strip()
            if not sentence:
                continue
                
            # 计算句子时长
            duration = len(sentence) * char_duration * 1000  # 转换为毫秒
            
            subtitle = {
                "index": i + 1,
                "content": sentence,
                "start": int(current_time),
                "end": int(current_time + duration),
                "start_time": current_time,
                "end_time": current_time + duration
            }
            
            subtitles.append(subtitle)
            current_time += duration + 200  # 添加200ms的句间停顿
        
        print(f"✅ 生成了 {len(subtitles)} 条字幕")
        
        # 更新结果
        result["subtitles"] = subtitles
        result["estimated_duration_ms"] = current_time
        result["estimated_duration_s"] = current_time / 1000
        
        return result
    
    def _split_text_to_sentences(self, text: str) -> list:
        """将文本分割成句子"""
        import re
        
        # 支持中英文标点符号
        sentence_endings = r'[。！？.!?]+'
        sentences = re.split(sentence_endings, text)
        
        # 如果没有找到句子结束符，按逗号分割
        if len(sentences) <= 1:
            sentences = re.split(r'[，,；;]', text)
            
        # 如果还是太长，按固定长度分割
        max_length = 50  # 每个字幕最多50个字符
        final_sentences = []
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            if len(sentence) <= max_length:
                final_sentences.append(sentence)
            else:
                # 分割长句子，考虑英文单词边界
                import re
                has_english = bool(re.search(r'[a-zA-Z]', sentence))
                
                if has_english:
                    # 对于包含英文的句子，按单词边界分割
                    words = sentence.split()
                    current_chunk = []
                    current_length = 0
                    
                    for word in words:
                        word_length = len(word)
                        # 如果加上这个单词会超过限制，且当前块不为空
                        if current_length + word_length + len(current_chunk) - 1 > max_length and current_chunk:
                            # 保存当前块
                            final_sentences.append(' '.join(current_chunk))
                            # 开始新块
                            current_chunk = [word]
                            current_length = word_length
                        else:
                            current_chunk.append(word)
                            current_length += word_length
                    
                    # 添加最后一块
                    if current_chunk:
                        final_sentences.append(' '.join(current_chunk))
                else:
                    # 对于纯中文，按字符分割
                    for i in range(0, len(sentence), max_length):
                        final_sentences.append(sentence[i:i+max_length])
                    
        return final_sentences


# GPT TTS 音色选项
GPT_VOICE_OPTIONS = {
    "标准音色": {
        "alloy": "Alloy - 中性、平衡的声音",
        "echo": "Echo - 温暖、友好的男声",
        "fable": "Fable - 英式英语、表现力强",
        "onyx": "Onyx - 深沉、共鸣的男声",
        "nova": "Nova - 年轻、充满活力的女声",
        "shimmer": "Shimmer - 柔和、愉快的女声"
    }
}

# 模型选项
GPT_MODEL_OPTIONS = {
    "tts-1": "标准质量 - 更快的生成速度",
    "tts-1-hd": "高质量 - 更好的音质"
}

# 格式选项
GPT_FORMAT_OPTIONS = {
    "mp3": "MP3 - 通用格式，文件较小",
    "opus": "Opus - 高质量、低延迟",
    "aac": "AAC - Apple设备优化",
    "flac": "FLAC - 无损音质"
}


def format_time_for_srt(milliseconds: int) -> str:
    """将毫秒转换为SRT时间格式"""
    total_seconds = milliseconds / 1000
    hours = int(total_seconds // 3600)
    minutes = int((total_seconds % 3600) // 60)
    seconds = int(total_seconds % 60)
    ms = int((total_seconds % 1) * 1000)
    
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{ms:03d}"


def convert_gpt_subtitles_to_srt(subtitles: list) -> str:
    """将GPT TTS生成的字幕转换为SRT格式"""
    srt_content = []
    
    for subtitle in subtitles:
        # 序号
        srt_content.append(str(subtitle['index']))
        
        # 时间轴
        start_time = format_time_for_srt(subtitle['start'])
        end_time = format_time_for_srt(subtitle['end'])
        srt_content.append(f"{start_time} --> {end_time}")
        
        # 文本内容
        srt_content.append(subtitle['content'])
        srt_content.append("")
    
    return '\n'.join(srt_content)


# 示例使用函数
def example_usage():
    """示例：如何使用GPT TTS API"""
    
    # 初始化API
    api_key = "sk-kmhk2OUYmq5SskSR2e2f1d9cCb5c4e9dB7A3118116548fDe"
    tts_api = GPTTextToSpeechAPI(api_key)
    
    # 示例文本
    text = "The quick brown fox jumped over the lazy dog. This is a test of the GPT text to speech API."
    
    # 生成音频和字幕
    result = tts_api.text_to_speech_with_subtitles(
        text=text,
        voice="alloy",
        model="tts-1",
        response_format="mp3"
    )
    
    if result:
        print(f"\n音频文件: {result['audio_path']}")
        print(f"预估时长: {result['estimated_duration_s']:.2f} 秒")
        print(f"\n字幕内容:")
        for subtitle in result['subtitles']:
            print(f"{subtitle['index']}. [{subtitle['start']/1000:.2f}s - {subtitle['end']/1000:.2f}s] {subtitle['content']}")
        
        # 生成SRT文件
        srt_content = convert_gpt_subtitles_to_srt(result['subtitles'])
        srt_path = result['audio_path'].replace('.mp3', '.srt')
        with open(srt_path, 'w', encoding='utf-8') as f:
            f.write(srt_content)
        print(f"\nSRT字幕文件: {srt_path}")
        
        return result
    else:
        print("生成失败")
        return None


if __name__ == "__main__":
    example_usage()
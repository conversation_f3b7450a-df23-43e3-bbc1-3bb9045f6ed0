#!/usr/bin/env python3
"""
测试新功能：封面图和去除字幕标点
"""

from generate_video_from_images_v2 import VideoGeneratorV2

def test_subtitle_punctuation():
    """测试字幕标点去除功能"""
    print("=== 测试字幕标点去除 ===\n")
    
    # 创建测试字幕
    test_subtitles = [
        {
            'text': '这是第一句话。',
            'begin_time': 0,
            'end_time': 3000
        },
        {
            'text': '这是第二句话！还有更多内容？',
            'begin_time': 3000,
            'end_time': 6000
        },
        {
            'text': '最后一句，带逗号。',
            'begin_time': 6000,
            'end_time': 9000
        }
    ]
    
    generator = VideoGeneratorV2("test", "test")
    
    print("原始字幕：")
    for sub in test_subtitles:
        print(f"- {sub['text']}")
    
    print("\n分割后的字幕（应该去掉标点）：")
    all_split = []
    for sub in test_subtitles:
        split_subs = generator.split_long_subtitle(sub, max_duration=10, max_chars=100)
        all_split.extend(split_subs)
    
    for sub in all_split:
        print(f"- {sub['text']}")
    
    print("\n✓ 测试完成！检查上面的字幕是否已去除末尾标点。")

def test_cover_image():
    """测试封面图功能说明"""
    print("\n\n=== 封面图功能说明 ===\n")
    
    print("新功能已添加：")
    print("1. 自动使用第一张图片作为视频封面")
    print("2. 封面图会保存为 output_video_cover.jpg")
    print("3. 封面图与视频同样是1920x1080分辨率")
    print("\n使用时会看到：")
    print("  生成视频封面...")
    print("  ✓ 封面图片已保存: output_video_cover.jpg")
    
    print("\n输出文件：")
    print("  - output_video.mp4 (视频文件)")
    print("  - output_video_cover.jpg (封面图)")
    print("  - output_video.srt (字幕文件)")

if __name__ == "__main__":
    # 测试字幕标点去除
    test_subtitle_punctuation()
    
    # 说明封面图功能
    test_cover_image()
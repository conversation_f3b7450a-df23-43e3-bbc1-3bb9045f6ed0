"""
YouTube视频上传模块
处理视频上传、元数据设置、缩略图上传等功能
"""

import os
import time
import logging
import mimetypes
from typing import Optional, Dict, Any, List
from datetime import datetime
import random

from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaFileUpload
from google.auth.exceptions import RefreshError

from youtube_auth import YouTubeAuthManager, YouTubeQuotaTracker
from youtube_database import YouTubeDatabase

logger = logging.getLogger(__name__)

# YouTube视频分类ID映射
YOUTUBE_CATEGORIES = {
    '1': 'Film & Animation',
    '2': 'Autos & Vehicles',
    '10': 'Music',
    '15': 'Pets & Animals',
    '17': 'Sports',
    '18': 'Short Movies',
    '19': 'Travel & Events',
    '20': 'Gaming',
    '21': 'Videoblogging',
    '22': 'People & Blogs',
    '23': 'Comedy',
    '24': 'Entertainment',
    '25': 'News & Politics',
    '26': 'Howto & Style',
    '27': 'Education',
    '28': 'Science & Technology',
    '29': 'Nonprofits & Activism',
    '30': 'Movies',
    '31': 'Anime/Animation',
    '32': 'Action/Adventure',
    '33': 'Classics',
    '34': 'Comedy',
    '35': 'Documentary',
    '36': 'Drama',
    '37': 'Family',
    '38': 'Foreign',
    '39': 'Horror',
    '40': 'Sci-Fi/Fantasy',
    '41': 'Thriller',
    '42': 'Shorts',
    '43': 'Shows',
    '44': 'Trailers'
}

class YouTubeUploader:
    """YouTube视频上传器"""
    
    def __init__(self, auth_manager: YouTubeAuthManager, db: YouTubeDatabase):
        self.auth_manager = auth_manager
        self.db = db
        self.quota_tracker = YouTubeQuotaTracker()
        self.retry_delays = [5, 10, 30, 60, 120]  # 重试延迟（秒）
    
    def upload_video(self, task_id: int) -> Dict[str, Any]:
        """
        执行视频上传任务
        
        Args:
            task_id: 上传任务ID
            
        Returns:
            上传结果字典
        """
        # 获取任务信息
        tasks = self.db.get_pending_upload_tasks(limit=1000)
        task = next((t for t in tasks if t['id'] == task_id), None)
        
        if not task:
            raise ValueError(f"找不到任务: {task_id}")
        
        video_id = task['video_id']
        account_id = task['account_id']
        
        # 记录开始上传
        self.db.update_upload_task(task_id, 'processing', started_at=datetime.now())
        self.db.add_upload_log(task_id, account_id, video_id, 'upload_start', 
                             '开始上传视频')
        
        try:
            # 检查配额
            if not self.quota_tracker.can_upload(account_id):
                raise Exception("账号配额不足，请稍后再试")
            
            # 获取YouTube服务
            service = self.auth_manager.get_youtube_service(task['credentials_path'])
            
            # 准备视频元数据
            video_metadata = self._prepare_video_metadata(task)
            
            # 上传视频
            youtube_video = self._upload_video_file(
                service, 
                task['file_path'],
                video_metadata,
                task_id,
                account_id,
                video_id
            )
            
            # 记录配额使用
            self.quota_tracker.track_usage(account_id, 'videos.insert')
            
            # 上传缩略图（如果有）
            if task.get('thumbnail_path') and os.path.exists(task['thumbnail_path']):
                self._upload_thumbnail(service, youtube_video['id'], task['thumbnail_path'])
            
            # 更新任务状态
            self.db.update_upload_task(
                task_id, 
                'completed',
                completed_at=datetime.now(),
                youtube_video_id=youtube_video['id'],
                youtube_url=f"https://www.youtube.com/watch?v={youtube_video['id']}"
            )
            
            # 更新视频状态
            self.db.update_video_status(video_id, 'uploaded')
            
            # 记录成功日志
            self.db.add_upload_log(
                task_id, account_id, video_id, 
                'upload_complete', 
                '视频上传成功',
                {
                    'youtube_id': youtube_video['id'],
                    'youtube_url': f"https://www.youtube.com/watch?v={youtube_video['id']}"
                }
            )
            
            return {
                'success': True,
                'youtube_id': youtube_video['id'],
                'youtube_url': f"https://www.youtube.com/watch?v={youtube_video['id']}",
                'title': youtube_video['snippet']['title']
            }
            
        except Exception as e:
            logger.error(f"上传失败: {str(e)}")
            
            # 更新任务状态
            retry_count = task.get('retry_count', 0) + 1
            self.db.update_upload_task(
                task_id,
                'failed' if retry_count >= task.get('max_retries', 3) else 'pending',
                error_message=str(e),
                retry_count=retry_count
            )
            
            # 记录失败日志
            self.db.add_upload_log(
                task_id, account_id, video_id,
                'upload_failed',
                f'上传失败: {str(e)}',
                {'error': str(e), 'retry_count': retry_count}
            )
            
            return {
                'success': False,
                'error': str(e),
                'retry_count': retry_count
            }
    
    def _prepare_video_metadata(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """准备视频元数据"""
        # 基础元数据
        metadata = {
            'snippet': {
                'title': task.get('title', 'Untitled Video'),
                'description': task.get('description', ''),
                'tags': task.get('tags', []),
                'categoryId': task.get('category_id', '22'),  # 默认People & Blogs
            },
            'status': {
                'privacyStatus': task.get('privacy_status', 'private'),
                'selfDeclaredMadeForKids': False,
            }
        }
        
        # 添加默认语言
        if task.get('default_language'):
            metadata['snippet']['defaultLanguage'] = task['default_language']
        
        # 添加录制日期
        if task.get('recorded_date'):
            metadata['recordingDetails'] = {
                'recordingDate': task['recorded_date']
            }
        
        return metadata
    
    def _upload_video_file(self, service, file_path: str, metadata: Dict[str, Any],
                          task_id: int, account_id: int, video_id: int) -> Dict[str, Any]:
        """上传视频文件到YouTube"""
        # 确定MIME类型
        mime_type, _ = mimetypes.guess_type(file_path)
        if not mime_type or not mime_type.startswith('video/'):
            mime_type = 'video/mp4'
        
        # 创建媒体上传对象
        media = MediaFileUpload(
            file_path,
            mimetype=mime_type,
            resumable=True,
            chunksize=10 * 1024 * 1024  # 10MB chunks
        )
        
        # 创建上传请求
        request = service.videos().insert(
            part=','.join(metadata.keys()),
            body=metadata,
            media_body=media
        )
        
        # 执行可恢复上传
        response = None
        error = None
        retry = 0
        
        while response is None:
            try:
                status, response = request.next_chunk()
                
                if status:
                    # 记录上传进度
                    progress = int(status.progress() * 100)
                    self.db.add_upload_log(
                        task_id, account_id, video_id,
                        'upload_progress',
                        f'上传进度: {progress}%',
                        {'progress': progress}
                    )
                    logger.info(f"上传进度: {progress}%")
                
            except HttpError as e:
                if e.resp.status in [500, 502, 503, 504]:
                    # 服务器错误，重试
                    error = f"服务器错误: {e}"
                    retry += 1
                    if retry > len(self.retry_delays):
                        raise
                    
                    delay = self.retry_delays[retry - 1]
                    logger.warning(f"上传失败，{delay}秒后重试: {error}")
                    time.sleep(delay)
                    
                elif e.resp.status == 401:
                    # 认证失败
                    raise Exception("认证失败，请重新授权")
                    
                elif e.resp.status == 403:
                    # 配额超限或权限问题
                    if 'quota' in str(e).lower():
                        raise Exception("API配额超限")
                    else:
                        raise Exception(f"权限错误: {e}")
                else:
                    raise
                    
            except Exception as e:
                # 其他错误
                logger.error(f"上传异常: {str(e)}")
                raise
        
        logger.info(f"视频上传成功: {response['id']}")
        return response
    
    def _upload_thumbnail(self, service, youtube_video_id: str, thumbnail_path: str):
        """上传视频缩略图"""
        try:
            # 确定MIME类型
            mime_type, _ = mimetypes.guess_type(thumbnail_path)
            if not mime_type or not mime_type.startswith('image/'):
                mime_type = 'image/jpeg'
            
            media = MediaFileUpload(
                thumbnail_path,
                mimetype=mime_type,
                resumable=True
            )
            
            request = service.thumbnails().set(
                videoId=youtube_video_id,
                media_body=media
            )
            
            response = request.execute()
            logger.info(f"缩略图上传成功: {youtube_video_id}")
            
        except Exception as e:
            logger.error(f"缩略图上传失败: {str(e)}")
            # 缩略图上传失败不应影响视频上传
    
    def batch_upload(self, video_ids: List[int], account_ids: List[int],
                    schedule_minutes: int = 0) -> List[int]:
        """
        批量创建上传任务
        
        Args:
            video_ids: 视频ID列表
            account_ids: 账号ID列表（会循环使用）
            schedule_minutes: 延迟发布的分钟数
            
        Returns:
            创建的任务ID列表
        """
        task_ids = []
        scheduled_time = None
        
        if schedule_minutes > 0:
            scheduled_time = datetime.now() + timedelta(minutes=schedule_minutes)
        
        for i, video_id in enumerate(video_ids):
            # 循环使用账号
            account_id = account_ids[i % len(account_ids)]
            
            try:
                # 检查视频是否存在
                video = self.db.get_video(video_id)
                if not video:
                    logger.warning(f"视频不存在: {video_id}")
                    continue
                
                # 创建上传任务
                task_id = self.db.create_upload_task(
                    video_id,
                    account_id,
                    scheduled_time=scheduled_time,
                    priority=0
                )
                
                task_ids.append(task_id)
                logger.info(f"创建上传任务: {task_id} (视频:{video_id}, 账号:{account_id})")
                
                # 如果设置了延迟，每个任务延迟一定时间
                if scheduled_time:
                    scheduled_time = scheduled_time + timedelta(minutes=random.randint(5, 15))
                
            except Exception as e:
                logger.error(f"创建任务失败 (视频:{video_id}): {str(e)}")
        
        return task_ids
    
    def update_video_metadata(self, youtube_video_id: str, credentials_path: str,
                            updates: Dict[str, Any]) -> bool:
        """
        更新已上传视频的元数据
        
        Args:
            youtube_video_id: YouTube视频ID
            credentials_path: 凭证文件路径
            updates: 要更新的字段
            
        Returns:
            是否成功
        """
        try:
            service = self.auth_manager.get_youtube_service(credentials_path)
            
            # 获取当前视频信息
            response = service.videos().list(
                part='snippet,status',
                id=youtube_video_id
            ).execute()
            
            if not response['items']:
                raise ValueError(f"视频不存在: {youtube_video_id}")
            
            video = response['items'][0]
            
            # 更新字段
            if 'title' in updates:
                video['snippet']['title'] = updates['title']
            if 'description' in updates:
                video['snippet']['description'] = updates['description']
            if 'tags' in updates:
                video['snippet']['tags'] = updates['tags']
            if 'categoryId' in updates:
                video['snippet']['categoryId'] = updates['categoryId']
            if 'privacyStatus' in updates:
                video['status']['privacyStatus'] = updates['privacyStatus']
            
            # 执行更新
            request = service.videos().update(
                part='snippet,status',
                body=video
            )
            
            response = request.execute()
            logger.info(f"视频元数据更新成功: {youtube_video_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新视频元数据失败: {str(e)}")
            return False
    
    def delete_video(self, youtube_video_id: str, credentials_path: str) -> bool:
        """删除YouTube视频"""
        try:
            service = self.auth_manager.get_youtube_service(credentials_path)
            
            request = service.videos().delete(id=youtube_video_id)
            request.execute()
            
            logger.info(f"视频删除成功: {youtube_video_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除视频失败: {str(e)}")
            return False
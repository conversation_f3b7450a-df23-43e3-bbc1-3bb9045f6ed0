#!/usr/bin/env python3
"""
YouTube视频管理系统启动脚本
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from youtube_manager_gui import main

if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('youtube_manager.log'),
            logging.StreamHandler()
        ]
    )
    
    # 启动应用
    main()
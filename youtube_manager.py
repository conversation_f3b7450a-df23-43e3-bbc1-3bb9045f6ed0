#!/usr/bin/env python3
"""
YouTube视频管理系统启动脚本
"""

import sys
import os
import logging
from pathlib import Path

# 修复PySide6在macOS上的环境变量问题
if sys.platform == 'darwin':  # macOS
    # 设置Qt插件路径
    qt_plugin_path = os.path.join(os.path.dirname(sys.executable), '..', 'lib', 'python3.10', 'site-packages', 'PySide6', 'Qt', 'plugins')
    if os.path.exists(qt_plugin_path):
        os.environ['QT_PLUGIN_PATH'] = qt_plugin_path

    # 设置Qt库路径
    qt_lib_path = os.path.join(os.path.dirname(sys.executable), '..', 'lib', 'python3.10', 'site-packages', 'PySide6', 'Qt', 'lib')
    if os.path.exists(qt_lib_path):
        if 'DYLD_LIBRARY_PATH' in os.environ:
            os.environ['DYLD_LIBRARY_PATH'] = qt_lib_path + ':' + os.environ['DYLD_LIBRARY_PATH']
        else:
            os.environ['DYLD_LIBRARY_PATH'] = qt_lib_path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from youtube_manager_gui import main
except ImportError as e:
    print(f"导入错误: {e}")
    print("正在尝试使用命令行界面...")
    try:
        from youtube_manager_cli import main as cli_main
        main = cli_main
    except ImportError:
        print("无法导入GUI或CLI界面，请检查依赖安装")
        sys.exit(1)

if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('youtube_manager.log'),
            logging.StreamHandler()
        ]
    )
    
    # 启动应用
    main()
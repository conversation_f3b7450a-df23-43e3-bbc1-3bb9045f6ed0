#!/usr/bin/env python3
"""
检查pyJianYingDraft中可用的动画类型
"""

def check_animation_types():
    """检查动画类型"""
    try:
        import pyJianYingDraft as draft
        
        print("🔍 检查pyJianYingDraft动画类型")
        print("=" * 50)
        
        # 检查Intro_type
        print("\n📥 Intro_type (入场动画):")
        try:
            intro_attrs = [attr for attr in dir(draft.Intro_type) if not attr.startswith('_')]
            print(f"   总共 {len(intro_attrs)} 个可用动画:")
            for i, attr in enumerate(intro_attrs, 1):
                print(f"      {i:2d}. {attr}")

        except Exception as e:
            print(f"   ❌ 检查Intro_type失败: {str(e)}")

        # 检查Outro_type
        print("\n📤 Outro_type (出场动画):")
        try:
            outro_attrs = [attr for attr in dir(draft.Outro_type) if not attr.startswith('_')]
            print(f"   总共 {len(outro_attrs)} 个可用动画:")
            for i, attr in enumerate(outro_attrs, 1):
                print(f"      {i:2d}. {attr}")

        except Exception as e:
            print(f"   ❌ 检查Outro_type失败: {str(e)}")
        
        # 检查Group_animation_type
        print("\n🎭 Group_animation_type (组合动画):")
        try:
            group_attrs = [attr for attr in dir(draft.Group_animation_type) if not attr.startswith('_')]
            print(f"   可用属性: {group_attrs}")
        except Exception as e:
            print(f"   ❌ 检查Group_animation_type失败: {str(e)}")
        
        # 尝试创建一个简单的测试
        print("\n🧪 创建测试片段:")
        try:
            # 创建一个简单的script
            script = draft.Script_file(1920, 1080)
            script.add_track(draft.Track_type.video, "test")
            
            # 创建一个测试图片路径（不需要真实存在）
            test_image_path = "test.png"
            
            # 尝试创建Video_segment
            test_segment = draft.Video_segment(
                test_image_path,
                draft.trange(0, 5000000)  # 5秒
            )
            
            print("   ✅ Video_segment创建成功")
            
            # 尝试添加动画（不使用中文名称）
            if hasattr(draft.Intro_type, 'fade_in'):
                test_segment.add_animation(draft.Intro_type.fade_in)
                print("   ✅ fade_in动画添加成功")
            elif hasattr(draft.Intro_type, 'FadeIn'):
                test_segment.add_animation(draft.Intro_type.FadeIn)
                print("   ✅ FadeIn动画添加成功")
            elif len([attr for attr in dir(draft.Intro_type) if not attr.startswith('_')]) > 0:
                # 使用第一个可用的动画
                first_intro = [attr for attr in dir(draft.Intro_type) if not attr.startswith('_')][0]
                test_segment.add_animation(getattr(draft.Intro_type, first_intro))
                print(f"   ✅ {first_intro}动画添加成功")
            else:
                print("   ⚠️  没有找到可用的入场动画")
            
        except Exception as e:
            print(f"   ❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "=" * 50)
        print("🎯 建议:")
        print("1. 不使用中文动画名称")
        print("2. 使用英文动画名称，如fade_in, fade_out")
        print("3. 或者完全不添加动画")
        
    except ImportError as e:
        print(f"❌ 无法导入pyJianYingDraft: {str(e)}")
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_animation_types()

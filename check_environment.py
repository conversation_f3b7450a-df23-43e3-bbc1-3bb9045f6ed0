#!/usr/bin/env python3
"""
环境检查脚本
检查YouTube管理系统的依赖是否正确安装
"""

import sys
import os
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    print("=== Python版本检查 ===")
    print(f"当前Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    major, minor = sys.version_info[:2]
    if major == 3 and minor >= 8:
        print("✅ Python版本符合要求 (>=3.8)")
        return True
    else:
        print("❌ Python版本过低，建议使用Python 3.8或以上版本")
        return False

def check_module(module_name, package_name=None):
    """检查模块是否安装"""
    if package_name is None:
        package_name = module_name
    
    try:
        __import__(module_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装")
        return False

def check_pyside6_detailed():
    """详细检查PySide6"""
    print("\n=== PySide6详细检查 ===")
    
    try:
        # 设置环境变量，可能有助于解决问题
        if platform.system() == "Darwin":  # macOS
            os.environ['QT_MAC_WANTS_LAYER'] = '1'
        
        # 尝试导入PySide6的各个组件
        print("尝试导入PySide6核心组件...")
        
        try:
            import PySide6
            print(f"✅ PySide6版本: {PySide6.__version__}")
        except Exception as e:
            print(f"❌ 导入PySide6失败: {e}")
            return False
        
        try:
            from PySide6 import QtCore
            print("✅ QtCore导入成功")
        except Exception as e:
            print(f"❌ QtCore导入失败: {e}")
            return False
        
        try:
            from PySide6 import QtWidgets
            print("✅ QtWidgets导入成功")
        except Exception as e:
            print(f"❌ QtWidgets导入失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ PySide6检查失败: {e}")
        return False

def check_google_api():
    """检查Google API客户端"""
    print("\n=== Google API客户端检查 ===")
    
    modules = [
        ('googleapiclient', 'google-api-python-client'),
        ('google.auth', 'google-auth'),
        ('google_auth_oauthlib', 'google-auth-oauthlib'),
    ]
    
    all_installed = True
    for module, package in modules:
        if not check_module(module, package):
            all_installed = False
    
    return all_installed

def check_ffmpeg():
    """检查ffmpeg是否安装"""
    print("\n=== FFmpeg检查 ===")
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ FFmpeg已安装: {version_line}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ FFmpeg未安装（可选，用于视频元数据提取）")
    print("   安装方法:")
    print("   - macOS: brew install ffmpeg")
    print("   - Ubuntu: sudo apt install ffmpeg")
    print("   - Windows: 从 https://ffmpeg.org/download.html 下载")
    return False

def suggest_fixes():
    """提供修复建议"""
    print("\n=== 修复建议 ===")
    
    print("\n1. 如果PySide6安装失败，请尝试:")
    print("   # 卸载现有版本")
    print("   pip uninstall PySide6 PySide6-Addons PySide6-Essentials shiboken6")
    print("   # 重新安装")
    print("   pip install PySide6==6.5.3  # 尝试使用稍旧的稳定版本")
    
    print("\n2. 如果仍有问题，可以使用命令行版本:")
    print("   python youtube_manager_cli.py")
    
    print("\n3. 在macOS上，如果遇到Qt相关错误:")
    print("   export QT_MAC_WANTS_LAYER=1")
    
    print("\n4. 安装所有依赖:")
    print("   pip install -r requirements_youtube.txt")

def main():
    print("YouTube管理系统环境检查工具")
    print("=" * 50)
    
    # 检查各项依赖
    python_ok = check_python_version()
    google_ok = check_google_api()
    pyside6_ok = check_pyside6_detailed()
    ffmpeg_ok = check_ffmpeg()
    
    # 检查其他依赖
    print("\n=== 其他依赖检查 ===")
    check_module('requests')
    
    # 总结
    print("\n" + "=" * 50)
    print("检查总结:")
    
    if python_ok and google_ok:
        print("✅ 核心功能依赖已满足（可以使用命令行版本）")
    else:
        print("❌ 核心功能依赖未满足")
    
    if pyside6_ok:
        print("✅ GUI功能依赖已满足")
    else:
        print("❌ GUI功能依赖未满足（但可以使用命令行版本）")
    
    if not (python_ok and google_ok and pyside6_ok):
        suggest_fixes()
    
    return python_ok and google_ok

if __name__ == '__main__':
    main()
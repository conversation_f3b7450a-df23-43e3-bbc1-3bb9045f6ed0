#!/usr/bin/env python3
"""
测试自定义提示词功能
"""

import asyncio
import json
from volcano_api import get_ai_prompt_from_subtitles

# 测试数据
test_subtitles = [
    {
        "content": "欢迎来到AI的世界",
        "start": 0,
        "end": 2000
    },
    {
        "content": "这里有无限的可能性",
        "start": 2000,
        "end": 4000
    },
    {
        "content": "让我们一起探索未来",
        "start": 4000,
        "end": 6000
    }
]

# 自定义提示词模板
custom_prompt_template = """你是一名专业的分镜设计师，请将字幕转换为温馨家庭风格的AI图像提示词。

要求：
1. 输出JSON数组格式
2. 每个元素包含 start, end, img_prompt 字段  
3. img_prompt 为英文描述，风格为温馨家庭场景
4. 画面要体现家庭温暖、亲情和谐的氛围

示例输出格式：
[
  {
    "start": 1000,
    "end": 3000,
    "img_prompt": "warm family scene, cozy living room, soft lighting, happy family gathering"
  }
]

请严格按照JSON格式输出，不要添加任何解释文字。"""

async def test_default_prompt():
    """测试默认提示词"""
    print("=== 测试默认提示词 ===")
    result = await get_ai_prompt_from_subtitles(test_subtitles)
    if result:
        print("✅ 默认提示词测试成功")
        print(f"生成了 {len(result)} 个场景")
        for i, scene in enumerate(result):
            print(f"场景 {i+1}: {scene['start']}ms - {scene['end']}ms")
            print(f"提示词: {scene['img_prompt'][:100]}...")
    else:
        print("❌ 默认提示词测试失败")
    print()

async def test_custom_prompt():
    """测试自定义提示词"""
    print("=== 测试自定义提示词 ===")
    result = await get_ai_prompt_from_subtitles(test_subtitles, custom_prompt=custom_prompt_template)
    if result:
        print("✅ 自定义提示词测试成功")
        print(f"生成了 {len(result)} 个场景")
        for i, scene in enumerate(result):
            print(f"场景 {i+1}: {scene['start']}ms - {scene['end']}ms")
            print(f"提示词: {scene['img_prompt'][:100]}...")
    else:
        print("❌ 自定义提示词测试失败")
    print()

async def main():
    """主测试函数"""
    print("开始测试自定义提示词功能...\n")
    
    # 测试默认提示词
    await test_default_prompt()
    
    # 测试自定义提示词
    await test_custom_prompt()
    
    print("测试完成！")

if __name__ == "__main__":
    asyncio.run(main())

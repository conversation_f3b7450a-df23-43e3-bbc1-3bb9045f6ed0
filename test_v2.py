#!/usr/bin/env python3
"""
测试V2版本视频生成器
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import platform

def test_pil_text():
    """测试PIL文字绘制功能"""
    print("=== 测试PIL文字绘制 ===")
    
    try:
        # 创建测试图像
        img = Image.new('RGBA', (800, 600), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # 测试基本文字绘制
        draw.text((100, 100), "Hello World", fill=(255, 255, 0, 255))
        print("✓ PIL基本文字绘制正常")
        
        # 测试中文（需要使用支持中文的字体）
        try:
            # 尝试加载中文字体
            system = platform.system()
            if system == "Darwin":
                font_path = "/System/Library/Fonts/STHeiti Light.ttc"
            elif system == "Windows":
                font_path = "C:/Windows/Fonts/msyh.ttc"
            else:
                font_path = None
            
            if font_path and os.path.exists(font_path):
                font = ImageFont.truetype(font_path, 30)
                draw.text((100, 200), "你好世界", font=font, fill=(255, 255, 0, 255))
                print("✓ PIL中文绘制正常（使用系统字体）")
            else:
                print("⚠️ 跳过中文测试（需要中文字体）")
        except Exception as e:
            print(f"⚠️ 中文绘制测试失败: {e}（这不影响基本功能）")
        
        return True
    except Exception as e:
        print(f"✗ PIL文字绘制测试失败: {e}")
        return False

def test_font_loading():
    """测试字体加载"""
    print("\n=== 测试字体加载 ===")
    
    system = platform.system()
    
    if system == "Darwin":  # macOS
        test_fonts = [
            "/System/Library/Fonts/PingFang.ttc",
            "/System/Library/Fonts/STHeiti Light.ttc",
            "/System/Library/Fonts/STHeitiSC-Light.ttc",
        ]
    elif system == "Windows":
        test_fonts = [
            "C:/Windows/Fonts/msyh.ttc",
            "C:/Windows/Fonts/simhei.ttf",
        ]
    else:  # Linux
        test_fonts = [
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        ]
    
    found_font = False
    for font_path in test_fonts:
        if os.path.exists(font_path):
            try:
                font = ImageFont.truetype(font_path, 40)
                print(f"✓ 成功加载字体: {font_path}")
                
                # 测试字体绘制
                img = Image.new('RGBA', (800, 200), (0, 0, 0, 255))
                draw = ImageDraw.Draw(img)
                draw.text((50, 50), "测试中文 Test English", font=font, fill=(255, 255, 0, 255))
                print("✓ 字体绘制测试成功")
                found_font = True
                break
            except Exception as e:
                print(f"⚠️ 加载字体 {font_path} 失败: {e}")
    
    if not found_font:
        print("⚠️ 未找到合适的中文字体，将使用默认字体")
    
    return True

def test_v2_dependencies():
    """测试V2版本的依赖"""
    print("=== 测试V2依赖 ===")
    
    all_ok = True
    
    # 测试导入
    try:
        from generate_video_from_images_v2 import VideoGeneratorV2
        print("✓ VideoGeneratorV2 导入成功")
    except ImportError as e:
        print(f"✗ 无法导入 VideoGeneratorV2: {e}")
        all_ok = False
    
    # 测试PIL兼容性修复
    try:
        import PIL.Image
        if not hasattr(PIL.Image, 'ANTIALIAS'):
            PIL.Image.ANTIALIAS = PIL.Image.LANCZOS
        print("✓ PIL兼容性修复成功")
    except Exception as e:
        print(f"✗ PIL兼容性修复失败: {e}")
        all_ok = False
    
    return all_ok

def main():
    """运行所有测试"""
    print("V2版本视频生成器测试\n")
    
    all_pass = True
    
    # 运行测试
    all_pass &= test_pil_text()
    all_pass &= test_font_loading()
    all_pass &= test_v2_dependencies()
    
    print("\n" + "="*50)
    if all_pass:
        print("✅ 所有测试通过！")
        print("\n可以运行: python3 generate_video_from_images_v2.py")
        print("\n特点：")
        print("- 使用PIL生成字幕，不依赖ImageMagick")
        print("- 黄色字幕，黑色描边")
        print("- 自动适应字体大小")
        print("- 支持多行字幕")
    else:
        print("❌ 部分测试失败")

if __name__ == "__main__":
    main()
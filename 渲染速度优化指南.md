# 视频渲染速度优化指南

## 为什么视频渲染慢？

视频渲染是CPU密集型任务，涉及：
- 图片解码和缩放
- 视频编码（H.264）
- 音频处理
- 字幕合成

一个1分钟的1080p视频包含约1800帧画面，每帧都需要处理。

## 速度优化方案

### 1. 使用快速渲染模式（推荐）

**速度提升3-5倍！**

```bash
# 方法1：使用启动脚本
./run_fast.sh

# 方法2：设置环境变量
RENDER_QUALITY=fast python3 generate_video_from_images_v2.py
```

快速模式使用：
- `ultrafast` 预设（最快的x264编码预设）
- 24fps（而不是30fps）
- 较低的比特率（2000k）
- 自动使用所有CPU核心

### 2. 三种渲染模式对比

| 模式 | 速度 | 质量 | 文件大小 | 适用场景 |
|------|------|------|----------|----------|
| 快速 | ⚡⚡⚡⚡⚡ | ⭐⭐⭐ | 较小 | 预览、测试、日常使用 |
| 平衡 | ⚡⚡⚡ | ⭐⭐⭐⭐ | 适中 | 一般发布 |
| 高质量 | ⚡⚡ | ⭐⭐⭐⭐⭐ | 较大 | 专业发布、存档 |

### 3. 渲染时间预估

以1分钟视频为例（8核CPU）：

**快速模式：**
- 10张图片：约30-60秒
- 30张图片：约60-120秒
- 60张图片：约90-180秒

**平衡模式：**
- 10张图片：约90-180秒
- 30张图片：约180-300秒
- 60张图片：约240-420秒

**高质量模式：**
- 10张图片：约180-300秒
- 30张图片：约300-600秒
- 60张图片：约420-900秒

### 4. 其他优化技巧

#### 降低分辨率

修改 `generate_video_from_images_v2.py` 中的分辨率：

```python
# 原始 1920x1080
video = CompositeVideoClip(clips, size=(1920, 1080))

# 改为 720p
video = CompositeVideoClip(clips, size=(1280, 720))

# 或 480p
video = CompositeVideoClip(clips, size=(854, 480))
```

#### 减少图片数量

- 使用更少的图片
- 让每张图片显示更长时间

#### 硬件优化

1. **CPU**：
   - 关闭其他占用CPU的程序
   - 确保良好的散热（避免降频）

2. **存储**：
   - 使用SSD而不是HDD
   - 确保有足够的可用空间

3. **内存**：
   - 至少8GB RAM
   - 关闭其他占用内存的程序

### 5. FFmpeg GPU加速（高级）

如果有NVIDIA显卡，可以尝试GPU加速：

```bash
# 检查是否支持
ffmpeg -hwaccels

# 如果支持cuda，可以修改代码使用GPU编码
codec='h264_nvenc'  # NVIDIA GPU
# 或
codec='h264_videotoolbox'  # macOS GPU
```

注意：GPU加速需要修改代码，且不一定比CPU快。

### 6. 批量处理优化

如果需要处理多个视频：

```python
# 串行处理（慢）
for folder in folders:
    process_video(folder)

# 并行处理（快）- 需要足够的内存
from concurrent.futures import ProcessPoolExecutor

with ProcessPoolExecutor(max_workers=2) as executor:
    executor.map(process_video, folders)
```

## 快速开始

最简单的优化方法：

```bash
# 1. 使用快速模式
./run_fast.sh

# 2. 如果还是太慢，降低分辨率
# 修改代码中的 (1920, 1080) 为 (1280, 720)

# 3. 确保没有其他程序占用CPU
# 查看CPU使用情况
top  # Linux/macOS
# 或
# 任务管理器  # Windows
```

## 常见问题

**Q: 快速模式会影响质量吗？**
A: 会略微降低质量，但对于大多数用途来说足够了。如果需要高质量，使用平衡或高质量模式。

**Q: 为什么我的电脑比预估时间慢很多？**
A: 可能原因：
- CPU核心数较少
- CPU性能较低
- 同时运行其他程序
- 使用HDD而不是SSD
- 散热不好导致CPU降频

**Q: 可以中断渲染吗？**
A: 可以按 Ctrl+C 中断，但已渲染的部分会丢失。

**Q: 如何知道使用了多少CPU核心？**
A: 程序会自动显示CPU核心数，例如："使用快速渲染模式（CPU核心数: 8）"
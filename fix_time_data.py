#!/usr/bin/env python3
"""
修复数据库中错误的时间数据
"""

import sys
from database import Database

def fix_project_time_data(project_id: int, dry_run: bool = True):
    """修复项目的时间数据"""
    db = Database()
    
    print(f"🔧 {'预览' if dry_run else '修复'}项目 {project_id} 的时间数据")
    print("=" * 60)
    
    # 获取项目详情
    project = db.get_project_details(project_id)
    if not project:
        print(f"❌ 项目 {project_id} 不存在")
        return False
    
    print(f"📋 项目名称: {project['name']}")
    
    # 检查音频时长
    audio_duration = None
    if project['audio_files']:
        audio_duration = project['audio_files'][0].get('duration', 0)
        print(f"🎵 音频时长: {audio_duration:.2f} 秒")
    
    # 检查图片时间数据
    if not project['images']:
        print("❌ 项目中没有图片数据")
        return False
    
    success_images = [img for img in project['images'] if img['status'] == 'success']
    if not success_images:
        print("❌ 项目中没有成功的图片")
        return False
    
    print(f"\n🖼️  检查 {len(success_images)} 张图片的时间数据:")
    
    fixes_needed = []
    
    for img in success_images:
        start_time = img['start_time']
        end_time = img['end_time']
        scene_index = img['scene_index']
        img_id = img['id']
        
        print(f"\n场景 {scene_index} (ID: {img_id}):")
        print(f"   当前时间: {start_time:.2f}s - {end_time:.2f}s")
        
        # 检查是否需要修复
        needs_fix = False
        new_start_time = start_time
        new_end_time = end_time
        
        # 如果时间明显超出音频时长，可能是毫秒被当作秒存储了
        if audio_duration and start_time > audio_duration * 10:  # 超出10倍音频时长
            # 尝试除以1000
            new_start_time = start_time / 1000.0
            new_end_time = end_time / 1000.0
            needs_fix = True
            print(f"   🔍 检测到时间异常，可能需要除以1000")
            print(f"   修复后: {new_start_time:.2f}s - {new_end_time:.2f}s")
        
        # 检查修复后的时间是否合理
        if needs_fix:
            if audio_duration:
                if new_start_time < audio_duration and new_end_time <= audio_duration + 1:  # 允许1秒误差
                    fixes_needed.append({
                        'id': img_id,
                        'scene_index': scene_index,
                        'old_start': start_time,
                        'old_end': end_time,
                        'new_start': new_start_time,
                        'new_end': new_end_time
                    })
                    print(f"   ✅ 修复方案可行")
                else:
                    print(f"   ❌ 修复后时间仍然异常")
            else:
                print(f"   ⚠️  无法验证修复方案（缺少音频时长）")
        else:
            print(f"   ✅ 时间数据正常")
    
    if not fixes_needed:
        print(f"\n✅ 所有图片时间数据正常，无需修复")
        return True
    
    print(f"\n📊 修复摘要:")
    print(f"   需要修复的图片: {len(fixes_needed)} 张")
    
    if dry_run:
        print(f"\n🔍 预览模式 - 不会实际修改数据")
        print(f"   要执行修复，请运行: python fix_time_data.py {project_id} --fix")
        return True
    
    # 执行修复
    print(f"\n🔧 开始修复数据...")
    
    success_count = 0
    for fix in fixes_needed:
        try:
            # 更新数据库
            db.conn.execute(
                "UPDATE images SET start_time = ?, end_time = ? WHERE id = ?",
                (fix['new_start'], fix['new_end'], fix['id'])
            )
            
            print(f"   ✅ 场景 {fix['scene_index']}: {fix['old_start']:.2f}s → {fix['new_start']:.2f}s")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ 场景 {fix['scene_index']} 修复失败: {str(e)}")
    
    if success_count > 0:
        db.conn.commit()
        print(f"\n🎉 修复完成! 成功修复 {success_count}/{len(fixes_needed)} 张图片")
    else:
        print(f"\n❌ 修复失败，未修改任何数据")
    
    return success_count > 0

def fix_ai_prompts_time_data(project_id: int, dry_run: bool = True):
    """修复AI提示词的时间数据"""
    db = Database()
    
    print(f"\n🤖 {'预览' if dry_run else '修复'}AI提示词时间数据")
    print("-" * 40)
    
    # 获取项目详情
    project = db.get_project_details(project_id)
    if not project or not project['ai_prompts']:
        print("❌ 项目中没有AI提示词数据")
        return False
    
    # 检查音频时长
    audio_duration = None
    if project['audio_files']:
        audio_duration = project['audio_files'][0].get('duration', 0)
    
    fixes_needed = []
    
    for prompt in project['ai_prompts']:
        start_time = prompt['start_time']
        end_time = prompt['end_time']
        scene_index = prompt['scene_index']
        prompt_id = prompt['id']
        
        print(f"场景 {scene_index}: {start_time:.2f}s - {end_time:.2f}s")
        
        # 检查是否需要修复
        if audio_duration and start_time > audio_duration * 10:
            new_start_time = start_time / 1000.0
            new_end_time = end_time / 1000.0
            
            fixes_needed.append({
                'id': prompt_id,
                'scene_index': scene_index,
                'old_start': start_time,
                'old_end': end_time,
                'new_start': new_start_time,
                'new_end': new_end_time
            })
            print(f"   → 修复为: {new_start_time:.2f}s - {new_end_time:.2f}s")
    
    if not fixes_needed:
        print("✅ AI提示词时间数据正常")
        return True
    
    if not dry_run:
        # 执行修复
        for fix in fixes_needed:
            try:
                db.conn.execute(
                    "UPDATE ai_prompts SET start_time = ?, end_time = ? WHERE id = ?",
                    (fix['new_start'], fix['new_end'], fix['id'])
                )
            except Exception as e:
                print(f"❌ 修复AI提示词失败: {str(e)}")
                return False
        
        db.conn.commit()
        print(f"✅ AI提示词修复完成")
    
    return True

def main():
    if len(sys.argv) < 2:
        print("用法: python fix_time_data.py <project_id> [--fix]")
        print("  --fix: 实际执行修复（默认为预览模式）")
        sys.exit(1)
    
    try:
        project_id = int(sys.argv[1])
        dry_run = "--fix" not in sys.argv
        
        # 修复图片时间数据
        success1 = fix_project_time_data(project_id, dry_run)
        
        # 修复AI提示词时间数据
        success2 = fix_ai_prompts_time_data(project_id, dry_run)
        
        if success1 and success2:
            if dry_run:
                print(f"\n💡 预览完成，要执行修复请添加 --fix 参数")
            else:
                print(f"\n🎉 所有时间数据修复完成！")
        
    except ValueError:
        print("错误: project_id 必须是数字")
        sys.exit(1)

if __name__ == "__main__":
    main()

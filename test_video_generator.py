#!/usr/bin/env python3
"""
视频生成器测试脚本
用于测试各个功能节点是否正常工作
"""

import os
import sys
import numpy as np
from PIL import Image
import tempfile

def test_dependencies():
    """测试依赖是否正确安装"""
    print("=== 测试依赖安装 ===")
    
    # 测试moviepy
    try:
        from moviepy.editor import VideoClip, ImageClip, AudioFileClip, TextClip
        print("✓ moviepy 安装正常")
    except ImportError as e:
        print(f"✗ moviepy 安装失败: {e}")
        return False
    
    # 测试PIL
    try:
        from PIL import Image
        # 检查ANTIALIAS兼容性
        if hasattr(Image, 'ANTIALIAS'):
            print("✓ PIL 版本包含 ANTIALIAS")
        else:
            print("✓ PIL 新版本，使用 LANCZOS")
        print("✓ Pillow 安装正常")
    except ImportError as e:
        print(f"✗ Pillow 安装失败: {e}")
        return False
    
    # 测试numpy
    try:
        import numpy as np
        print("✓ numpy 安装正常")
    except ImportError as e:
        print(f"✗ numpy 安装失败: {e}")
        return False
    
    # 测试volcano_api
    try:
        from volcano_api import VolcanoSpeechAPI
        print("✓ volcano_api 模块可以导入")
    except ImportError as e:
        print(f"✗ volcano_api 导入失败: {e}")
        return False
    
    # 测试环境变量
    from dotenv import load_dotenv
    load_dotenv()
    
    if os.getenv('VOLCANO_APP_ID') and os.getenv('VOLCANO_ACCESS_TOKEN'):
        print("✓ 火山引擎API凭证已配置")
    else:
        print("⚠️ 火山引擎API凭证未配置（.env文件）")
    
    return True

def test_image_processing():
    """测试图片处理功能"""
    print("\n=== 测试图片处理 ===")
    
    try:
        # 创建测试图片
        test_img = Image.new('RGB', (800, 600), color='red')
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        test_img.save(temp_file.name)
        print(f"✓ 创建测试图片: {temp_file.name}")
        
        # 测试图片缩放
        if hasattr(Image, 'ANTIALIAS'):
            resample = Image.ANTIALIAS
        else:
            resample = Image.LANCZOS
            
        resized = test_img.resize((1920, 1080), resample)
        print(f"✓ 图片缩放成功: {resized.size}")
        
        # 清理
        os.unlink(temp_file.name)
        print("✓ 图片处理测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 图片处理测试失败: {e}")
        return False

def test_video_creation():
    """测试基本的视频创建功能"""
    print("\n=== 测试视频创建 ===")
    
    try:
        from moviepy.editor import ImageClip, ColorClip, CompositeVideoClip
        
        # 创建一个简单的视频片段
        clip = ColorClip(size=(640, 480), color=(0, 0, 255), duration=2)
        print("✓ 创建视频片段成功")
        
        # 测试是否可以设置参数
        clip = clip.set_duration(1)
        print("✓ 设置视频参数成功")
        
        # 不实际输出视频，只测试创建
        print("✓ 视频创建测试通过（未实际渲染）")
        return True
        
    except Exception as e:
        print(f"✗ 视频创建测试失败: {e}")
        return False

def test_font_availability():
    """测试字体可用性"""
    print("\n=== 测试字体可用性 ===")
    
    # 注意：TextClip需要ImageMagick，这不是必需的
    # 我们的视频生成器不依赖于TextClip来添加字幕
    print("⚠️ 字体测试跳过 - MoviePy的TextClip需要ImageMagick")
    print("  视频生成器将使用备用方案处理字幕")
    print("  如需完整字幕支持，可安装ImageMagick:")
    print("    macOS: brew install imagemagick")
    print("    Ubuntu: sudo apt-get install imagemagick")
    
    # 返回True因为这不是关键依赖
    return True

def test_ffmpeg():
    """测试ffmpeg是否可用"""
    print("\n=== 测试FFmpeg ===")
    
    import subprocess
    import platform
    
    try:
        # 尝试运行ffmpeg命令
        if platform.system() == "Windows":
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True, shell=True)
        else:
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
        
        if result.returncode == 0:
            # 提取版本信息
            version_line = result.stdout.split('\n')[0]
            print(f"✓ FFmpeg 已安装: {version_line}")
            
            # 同时测试moviepy是否能正常使用ffmpeg
            try:
                from moviepy.video.io.VideoFileClip import VideoFileClip
                from moviepy.video.io.ffmpeg_tools import ffmpeg_extract_subclip
                print("✓ MoviePy 可以正常调用 FFmpeg")
            except ImportError:
                # 这些工具可能在某些版本中不存在，不是关键错误
                pass
                
            return True
        else:
            print("✗ FFmpeg 命令执行失败")
            return False
            
    except FileNotFoundError:
        print("✗ FFmpeg 未安装或不在系统PATH中")
        print("  请安装ffmpeg:")
        print("    macOS: brew install ffmpeg")
        print("    Ubuntu/Debian: sudo apt-get install ffmpeg")
        print("    Windows: 从 https://ffmpeg.org/download.html 下载")
        return False
    except Exception as e:
        print(f"✗ 检查FFmpeg时出错: {e}")
        return False

def main():
    """运行所有测试"""
    print("视频生成器功能测试\n")
    
    all_pass = True
    
    # 运行各项测试
    all_pass &= test_dependencies()
    all_pass &= test_image_processing()
    all_pass &= test_video_creation()
    all_pass &= test_font_availability()
    all_pass &= test_ffmpeg()
    
    print("\n" + "="*50)
    if all_pass:
        print("✅ 所有测试通过！可以运行 generate_video_from_images.py")
    else:
        print("❌ 部分测试失败，请检查上述错误信息")
        print("\n常见解决方案：")
        print("1. 安装缺失的依赖: pip install -r requirements.txt")
        print("2. 安装ffmpeg: brew install ffmpeg (macOS)")
        print("3. 配置.env文件中的API凭证")

if __name__ == "__main__":
    main()
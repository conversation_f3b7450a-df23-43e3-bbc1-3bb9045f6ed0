"""
上传任务队列管理器
处理批量上传任务的调度和执行
"""

import time
import logging
import threading
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, Future
import queue

from youtube_database import YouTubeDatabase
from youtube_auth import YouTubeAuthManager
from youtube_uploader import YouTubeUploader

logger = logging.getLogger(__name__)

class UploadQueueManager:
    """上传任务队列管理器"""
    
    def __init__(self, youtube_db: YouTubeDatabase, auth_manager: YouTubeAuthManager,
                 max_workers: int = 3, check_interval: int = 60):
        self.youtube_db = youtube_db
        self.auth_manager = auth_manager
        self.uploader = YouTubeUploader(auth_manager, youtube_db)
        
        self.max_workers = max_workers
        self.check_interval = check_interval  # 检查新任务的间隔（秒）
        
        self._running = False
        self._executor = None
        self._monitor_thread = None
        self._active_tasks = {}  # task_id -> Future
        self._lock = threading.Lock()
        
        # 任务队列
        self._task_queue = queue.PriorityQueue()
        
        # 统计信息
        self._stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'start_time': None
        }
    
    def start(self):
        """启动队列管理器"""
        if self._running:
            logger.warning("队列管理器已在运行")
            return
        
        self._running = True
        self._stats['start_time'] = datetime.now()
        
        # 创建线程池
        self._executor = ThreadPoolExecutor(max_workers=self.max_workers)
        
        # 启动监控线程
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        logger.info(f"上传队列管理器已启动，最大并发数: {self.max_workers}")
    
    def stop(self):
        """停止队列管理器"""
        if not self._running:
            return
        
        self._running = False
        
        # 等待所有任务完成
        if self._executor:
            self._executor.shutdown(wait=True)
            self._executor = None
        
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        
        logger.info("上传队列管理器已停止")
    
    def _monitor_loop(self):
        """监控循环，定期检查新任务"""
        while self._running:
            try:
                # 检查并加载新任务
                self._load_pending_tasks()
                
                # 处理队列中的任务
                self._process_queue()
                
                # 清理完成的任务
                self._cleanup_completed_tasks()
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"监控循环错误: {str(e)}")
                time.sleep(5)  # 出错后短暂等待
    
    def _load_pending_tasks(self):
        """加载待处理的任务到队列"""
        try:
            # 获取待处理任务
            pending_tasks = self.youtube_db.get_pending_upload_tasks(limit=50)
            
            for task in pending_tasks:
                task_id = task['id']
                
                # 检查是否已在处理中
                with self._lock:
                    if task_id in self._active_tasks:
                        continue
                
                # 添加到优先队列（优先级越高，数字越小）
                priority = -task.get('priority', 0)  # 转换为负数以便高优先级先处理
                self._task_queue.put((priority, task_id, task))
                
                logger.debug(f"加载任务到队列: {task_id}, 优先级: {task.get('priority', 0)}")
                
        except Exception as e:
            logger.error(f"加载任务失败: {str(e)}")
    
    def _process_queue(self):
        """处理队列中的任务"""
        while not self._task_queue.empty() and self._running:
            with self._lock:
                # 检查是否还有可用的工作线程
                if len(self._active_tasks) >= self.max_workers:
                    break
            
            try:
                # 从队列获取任务
                priority, task_id, task = self._task_queue.get_nowait()
                
                # 再次检查任务是否已在处理
                with self._lock:
                    if task_id in self._active_tasks:
                        continue
                
                # 检查账号配额
                if not self._check_account_quota(task['account_id']):
                    logger.warning(f"账号 {task['account_id']} 配额不足，跳过任务 {task_id}")
                    # 将任务状态更新为等待
                    self.youtube_db.update_upload_task(
                        task_id, 'pending',
                        error_message='账号配额不足，等待明天'
                    )
                    continue
                
                # 提交任务到线程池
                future = self._executor.submit(self._execute_upload_task, task_id)
                
                with self._lock:
                    self._active_tasks[task_id] = future
                
                logger.info(f"开始处理上传任务: {task_id}")
                
            except queue.Empty:
                break
            except Exception as e:
                logger.error(f"处理队列任务失败: {str(e)}")
    
    def _execute_upload_task(self, task_id: int):
        """执行上传任务"""
        try:
            logger.info(f"执行上传任务: {task_id}")
            
            # 执行上传
            result = self.uploader.upload_video(task_id)
            
            # 更新统计
            with self._lock:
                self._stats['total_processed'] += 1
                if result['success']:
                    self._stats['successful'] += 1
                else:
                    self._stats['failed'] += 1
            
            return result
            
        except Exception as e:
            logger.error(f"执行上传任务失败 {task_id}: {str(e)}")
            
            # 更新任务状态
            self.youtube_db.update_upload_task(
                task_id, 'failed',
                error_message=str(e)
            )
            
            with self._lock:
                self._stats['total_processed'] += 1
                self._stats['failed'] += 1
            
            return {'success': False, 'error': str(e)}
    
    def _cleanup_completed_tasks(self):
        """清理已完成的任务"""
        completed = []
        
        with self._lock:
            for task_id, future in self._active_tasks.items():
                if future.done():
                    completed.append(task_id)
        
        for task_id in completed:
            with self._lock:
                del self._active_tasks[task_id]
            
            try:
                future = self._active_tasks.get(task_id)
                if future:
                    result = future.result()
                    logger.info(f"任务 {task_id} 完成: {result}")
            except Exception as e:
                logger.error(f"获取任务结果失败 {task_id}: {str(e)}")
    
    def _check_account_quota(self, account_id: int) -> bool:
        """检查账号配额是否充足"""
        # 使用配额跟踪器检查
        from youtube_auth import YouTubeQuotaTracker
        tracker = YouTubeQuotaTracker()
        return tracker.can_upload(account_id)
    
    def add_batch_upload_tasks(self, video_ids: List[int], account_ids: List[int],
                              schedule_config: Dict[str, Any] = None) -> List[int]:
        """
        批量添加上传任务
        
        Args:
            video_ids: 视频ID列表
            account_ids: 账号ID列表
            schedule_config: 调度配置
                - interval_minutes: 每个任务之间的间隔（分钟）
                - start_time: 开始时间
                - daily_limit: 每日上传限制
                
        Returns:
            创建的任务ID列表
        """
        task_ids = []
        
        # 默认配置
        interval_minutes = schedule_config.get('interval_minutes', 10) if schedule_config else 10
        start_time = schedule_config.get('start_time', datetime.now()) if schedule_config else datetime.now()
        daily_limit = schedule_config.get('daily_limit', 5) if schedule_config else 5
        
        # 计算每个账号的任务分配
        tasks_per_account = {}
        for account_id in account_ids:
            tasks_per_account[account_id] = 0
        
        scheduled_time = start_time
        account_index = 0
        
        for video_id in video_ids:
            # 循环使用账号
            account_id = account_ids[account_index % len(account_ids)]
            
            # 检查账号是否达到每日限制
            if tasks_per_account[account_id] >= daily_limit:
                # 跳到下一个账号
                account_index += 1
                if account_index >= len(account_ids):
                    # 所有账号都达到限制，推迟到明天
                    scheduled_time = scheduled_time.replace(hour=9, minute=0) + timedelta(days=1)
                    account_index = 0
                    # 重置计数
                    for aid in account_ids:
                        tasks_per_account[aid] = 0
                
                account_id = account_ids[account_index % len(account_ids)]
            
            try:
                # 创建上传任务
                task_id = self.youtube_db.create_upload_task(
                    video_id,
                    account_id,
                    scheduled_time=scheduled_time,
                    priority=0
                )
                
                task_ids.append(task_id)
                tasks_per_account[account_id] += 1
                
                logger.info(f"创建上传任务: {task_id} (视频:{video_id}, 账号:{account_id}, 计划时间:{scheduled_time})")
                
                # 计算下一个任务的时间
                scheduled_time += timedelta(minutes=interval_minutes)
                
                # 如果超过晚上10点，推迟到第二天早上9点
                if scheduled_time.hour >= 22:
                    scheduled_time = scheduled_time.replace(hour=9, minute=0) + timedelta(days=1)
                
            except Exception as e:
                logger.error(f"创建任务失败 (视频:{video_id}): {str(e)}")
        
        return task_ids
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        with self._lock:
            active_count = len(self._active_tasks)
            
        # 获取数据库中的任务统计
        db_stats = self.youtube_db.get_upload_statistics()
        
        # 计算运行时间
        uptime = None
        if self._stats['start_time']:
            uptime = str(datetime.now() - self._stats['start_time']).split('.')[0]
        
        return {
            'running': self._running,
            'uptime': uptime,
            'active_tasks': active_count,
            'max_workers': self.max_workers,
            'queue_size': self._task_queue.qsize(),
            'processed': self._stats['total_processed'],
            'successful': self._stats['successful'],
            'failed': self._stats['failed'],
            'success_rate': f"{self._stats['successful'] / self._stats['total_processed'] * 100:.1f}%" 
                           if self._stats['total_processed'] > 0 else "0%",
            'database_stats': db_stats
        }
    
    def pause_task(self, task_id: int) -> bool:
        """暂停任务"""
        try:
            # 如果任务在活跃列表中，取消它
            with self._lock:
                if task_id in self._active_tasks:
                    future = self._active_tasks[task_id]
                    future.cancel()
                    del self._active_tasks[task_id]
            
            # 更新任务状态
            self.youtube_db.update_upload_task(task_id, 'pending')
            logger.info(f"已暂停任务: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"暂停任务失败 {task_id}: {str(e)}")
            return False
    
    def cancel_task(self, task_id: int) -> bool:
        """取消任务"""
        try:
            # 如果任务在活跃列表中，取消它
            with self._lock:
                if task_id in self._active_tasks:
                    future = self._active_tasks[task_id]
                    future.cancel()
                    del self._active_tasks[task_id]
            
            # 更新任务状态
            self.youtube_db.update_upload_task(task_id, 'cancelled')
            logger.info(f"已取消任务: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消任务失败 {task_id}: {str(e)}")
            return False
    
    def retry_failed_tasks(self) -> int:
        """重试失败的任务"""
        try:
            # 获取失败的任务
            failed_tasks = self.youtube_db.get_pending_upload_tasks(limit=1000)
            retry_count = 0
            
            for task in failed_tasks:
                if task['status'] == 'failed' and task['retry_count'] < task['max_retries']:
                    # 重置任务状态
                    self.youtube_db.update_upload_task(
                        task['id'], 'pending',
                        error_message=None
                    )
                    retry_count += 1
            
            logger.info(f"已重置 {retry_count} 个失败任务")
            return retry_count
            
        except Exception as e:
            logger.error(f"重试失败任务出错: {str(e)}")
            return 0
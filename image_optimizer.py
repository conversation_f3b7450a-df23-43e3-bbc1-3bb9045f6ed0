import os
import json
from PIL import Image, ImageOps
from typing import List, Dict, Tuple, Optional
import shutil

class ImageOptimizer:
    """
    图片优化器，专门为剪映草稿优化图片
    """
    
    def __init__(self):
        self.target_width = 1920
        self.target_height = 1080
        self.target_ratio = self.target_width / self.target_height
    
    def optimize_images_for_project(self, project_images: List[Dict], output_dir: str) -> List[Dict]:
        """
        为项目优化所有图片
        Args:
            project_images: 项目图片信息列表
            output_dir: 输出目录
        Returns:
            优化后的图片信息列表
        """
        os.makedirs(output_dir, exist_ok=True)
        optimized_images = []
        
        for i, image_info in enumerate(project_images):
            if image_info['status'] != 'success':
                continue
                
            original_path = image_info['file_path']
            if not os.path.exists(original_path):
                print(f"原始图片不存在: {original_path}")
                continue
            
            try:
                # 生成优化后的文件名
                filename = f"optimized_scene_{image_info['scene_index']:03d}_{int(image_info['start_time']*1000)}_{int(image_info['end_time']*1000)}.png"
                output_path = os.path.join(output_dir, filename)
                
                # 优化图片
                success = self.optimize_single_image(original_path, output_path)
                
                if success:
                    # 创建新的图片信息
                    optimized_info = image_info.copy()
                    optimized_info['file_path'] = output_path
                    optimized_info['file_name'] = filename
                    optimized_info['optimized'] = True
                    optimized_images.append(optimized_info)
                    print(f"✅ 图片优化成功: {filename}")
                else:
                    print(f"❌ 图片优化失败: {original_path}")
                    
            except Exception as e:
                print(f"❌ 优化图片时出错: {str(e)}")
                continue
        
        print(f"🎨 图片优化完成: {len(optimized_images)}/{len(project_images)} 张图片")
        return optimized_images
    
    def optimize_single_image(self, input_path: str, output_path: str) -> bool:
        """
        优化单张图片
        """
        try:
            with Image.open(input_path) as img:
                # 转换为RGB模式（如果不是的话）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                original_width, original_height = img.size
                original_ratio = original_width / original_height
                
                print(f"📐 原始尺寸: {original_width}x{original_height} (比例: {original_ratio:.2f})")
                
                # 选择优化策略
                if abs(original_ratio - self.target_ratio) < 0.1:
                    # 比例接近，直接缩放
                    optimized_img = self._resize_image(img)
                    strategy = "直接缩放"
                elif original_ratio > self.target_ratio:
                    # 图片更宽，需要裁剪或添加上下黑边
                    optimized_img = self._handle_wide_image(img)
                    strategy = "宽图处理"
                else:
                    # 图片更高，需要裁剪或添加左右黑边
                    optimized_img = self._handle_tall_image(img)
                    strategy = "高图处理"
                
                # 保存优化后的图片
                optimized_img.save(output_path, 'PNG', quality=95, optimize=True)
                
                final_width, final_height = optimized_img.size
                print(f"🎯 优化后尺寸: {final_width}x{final_height} (策略: {strategy})")
                
                return True
                
        except Exception as e:
            print(f"❌ 处理图片时出错: {str(e)}")
            return False
    
    def _resize_image(self, img: Image.Image) -> Image.Image:
        """
        直接缩放图片到目标尺寸
        """
        return img.resize((self.target_width, self.target_height), Image.Resampling.LANCZOS)
    
    def _handle_wide_image(self, img: Image.Image) -> Image.Image:
        """
        处理宽图片（比例大于目标比例）
        """
        width, height = img.size
        
        # 计算按高度缩放后的宽度
        scale_factor = self.target_height / height
        new_width = int(width * scale_factor)
        
        if new_width <= self.target_width * 1.2:  # 如果宽度不超过目标太多，居中裁剪
            # 先按高度缩放
            img_resized = img.resize((new_width, self.target_height), Image.Resampling.LANCZOS)
            
            # 居中裁剪
            left = (new_width - self.target_width) // 2
            img_cropped = img_resized.crop((left, 0, left + self.target_width, self.target_height))
            return img_cropped
        else:
            # 图片太宽，添加上下黑边
            scale_factor = self.target_width / width
            new_height = int(height * scale_factor)
            
            # 缩放图片
            img_resized = img.resize((self.target_width, new_height), Image.Resampling.LANCZOS)
            
            # 创建黑色背景
            background = Image.new('RGB', (self.target_width, self.target_height), (0, 0, 0))
            
            # 居中粘贴
            y_offset = (self.target_height - new_height) // 2
            background.paste(img_resized, (0, y_offset))
            
            return background
    
    def _handle_tall_image(self, img: Image.Image) -> Image.Image:
        """
        处理高图片（比例小于目标比例）
        """
        width, height = img.size
        
        # 计算按宽度缩放后的高度
        scale_factor = self.target_width / width
        new_height = int(height * scale_factor)
        
        if new_height <= self.target_height * 1.2:  # 如果高度不超过目标太多，居中裁剪
            # 先按宽度缩放
            img_resized = img.resize((self.target_width, new_height), Image.Resampling.LANCZOS)
            
            # 居中裁剪
            top = (new_height - self.target_height) // 2
            img_cropped = img_resized.crop((0, top, self.target_width, top + self.target_height))
            return img_cropped
        else:
            # 图片太高，添加左右黑边
            scale_factor = self.target_height / height
            new_width = int(width * scale_factor)
            
            # 缩放图片
            img_resized = img.resize((new_width, self.target_height), Image.Resampling.LANCZOS)
            
            # 创建黑色背景
            background = Image.new('RGB', (self.target_width, self.target_height), (0, 0, 0))
            
            # 居中粘贴
            x_offset = (self.target_width - new_width) // 2
            background.paste(img_resized, (x_offset, 0))
            
            return background
    
    def analyze_image_info(self, image_path: str) -> Dict:
        """
        分析图片信息
        """
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                ratio = width / height
                file_size = os.path.getsize(image_path)
                
                return {
                    'width': width,
                    'height': height,
                    'ratio': ratio,
                    'mode': img.mode,
                    'format': img.format,
                    'file_size': file_size,
                    'target_ratio': self.target_ratio,
                    'ratio_diff': abs(ratio - self.target_ratio),
                    'needs_optimization': abs(ratio - self.target_ratio) > 0.1 or width != self.target_width or height != self.target_height
                }
        except Exception as e:
            return {'error': str(e)}
    
    def create_test_images(self, output_dir: str):
        """
        创建测试图片用于验证
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建不同比例的测试图片
        test_configs = [
            {'name': 'wide_image.png', 'size': (2560, 1080), 'color': (255, 100, 100)},
            {'name': 'tall_image.png', 'size': (1080, 1920), 'color': (100, 255, 100)},
            {'name': 'square_image.png', 'size': (1080, 1080), 'color': (100, 100, 255)},
            {'name': 'target_ratio.png', 'size': (1920, 1080), 'color': (255, 255, 100)},
        ]
        
        for config in test_configs:
            img = Image.new('RGB', config['size'], config['color'])
            output_path = os.path.join(output_dir, config['name'])
            img.save(output_path, 'PNG')
            print(f"创建测试图片: {output_path}")

def test_image_optimizer():
    """
    测试图片优化器
    """
    optimizer = ImageOptimizer()
    
    # 创建测试目录
    test_dir = "test_images"
    output_dir = "optimized_images"
    
    # 创建测试图片
    optimizer.create_test_images(test_dir)
    
    # 测试优化
    test_images = [
        {'file_path': os.path.join(test_dir, 'wide_image.png'), 'scene_index': 1, 'start_time': 0, 'end_time': 5, 'status': 'success'},
        {'file_path': os.path.join(test_dir, 'tall_image.png'), 'scene_index': 2, 'start_time': 5, 'end_time': 10, 'status': 'success'},
        {'file_path': os.path.join(test_dir, 'square_image.png'), 'scene_index': 3, 'start_time': 10, 'end_time': 15, 'status': 'success'},
    ]
    
    optimized = optimizer.optimize_images_for_project(test_images, output_dir)
    print(f"优化完成，共处理 {len(optimized)} 张图片")

if __name__ == "__main__":
    test_image_optimizer()

# YouTube视频管理系统

一个基于PySide6的YouTube视频批量上传管理系统，支持多账号矩阵管理、批量上传、任务队列等功能。

## 功能特点

### 1. 视频管理
- 自动扫描项目目录和根目录中的视频文件
- 视频信息管理（标题、描述、标签、分类等）
- 视频状态跟踪（生成、就绪、上传中、已上传、失败）
- 支持元数据模板批量应用

### 2. YouTube账号管理
- 支持多个YouTube账号
- OAuth2安全认证
- 账号配额跟踪
- 频道信息显示

### 3. 批量上传
- 多账号轮流上传
- 自定义发布间隔
- 每日上传限制
- 优先级队列

### 4. 任务队列
- 后台任务队列管理
- 并发上传控制
- 失败重试机制
- 实时状态监控

### 5. 日志系统
- 详细的上传日志
- 错误跟踪
- 操作历史记录

## 安装步骤

### 1. 安装依赖

```bash
pip install -r requirements_youtube.txt
```

### 2. 配置YouTube API

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 YouTube Data API v3
4. 创建 OAuth 2.0 客户端 ID（类型选择"桌面应用"）
5. 下载客户端密钥JSON文件

### 3. 启动系统

```bash
python youtube_manager.py
```

## 使用流程

### 1. 添加YouTube账号
1. 点击"添加账号"
2. 输入账号名称（用于识别）
3. 选择下载的客户端密钥JSON文件
4. 点击确定，浏览器会打开进行授权
5. 授权成功后账号会自动保存

### 2. 导入视频
1. 点击"扫描新视频"自动扫描所有视频
2. 系统会自动导入找到的视频文件
3. 支持的格式：mp4, avi, mov, mkv, webm等

### 3. 编辑视频信息
1. 在视频管理页面选择视频
2. 点击"编辑信息"
3. 填写标题、描述、标签等信息
4. 选择隐私设置（private/unlisted/public）

### 4. 批量上传
1. 切换到"批量上传"标签页
2. 选择要使用的YouTube账号（可多选）
3. 设置发布间隔和每日限制
4. 选择要上传的视频
5. 点击"创建上传任务"

### 5. 监控上传进度
1. 启动队列（点击工具栏的"启动队列"）
2. 在"任务监控"页面查看上传进度
3. 查看上传日志了解详细信息

## 数据库结构

系统使用SQLite数据库存储所有数据：

- **videos**: 视频信息表
- **youtube_accounts**: YouTube账号表
- **upload_tasks**: 上传任务表
- **upload_logs**: 上传日志表
- **video_metadata_templates**: 视频元数据模板表

## 文件组织

```
projects/
├── project_1/
│   ├── audio/
│   ├── subtitles/
│   ├── images/
│   └── videos/    # 视频文件存储位置
├── project_2/
│   └── ...
└── temp/          # 临时文件

credentials/       # YouTube账号凭证存储
youtube_manager.db # YouTube管理数据库
digitalpeople.db   # 项目数据库
```

## 注意事项

1. **API配额限制**：YouTube API每日配额为10,000单位，上传一个视频约消耗1,600单位
2. **并发控制**：默认最多3个并发上传，可在代码中调整
3. **隐私设置**：建议先使用private模式测试，确认无误后再改为public
4. **视频要求**：确保视频符合YouTube的要求（格式、大小、内容等）

## 故障排除

### 认证失败
- 检查客户端密钥文件是否正确
- 确保启用了YouTube Data API v3
- 检查OAuth同意屏幕配置

### 上传失败
- 检查网络连接
- 验证账号配额是否充足
- 查看上传日志了解详细错误

### 视频无法扫描
- 确保视频文件有正确的扩展名
- 检查文件权限
- 查看日志文件了解错误信息

## 扩展功能

系统预留了以下扩展接口：

1. 自定义视频处理插件
2. 批量视频编辑功能
3. 定时发布功能
4. 视频分析统计
5. 多平台支持（B站、抖音等）

## 开发说明

主要模块：
- `youtube_database.py`: 数据库管理
- `youtube_auth.py`: YouTube认证
- `youtube_uploader.py`: 视频上传
- `video_manager.py`: 视频管理
- `upload_queue_manager.py`: 任务队列
- `youtube_manager_gui.py`: PySide6界面

## 许可证

本项目仅供学习交流使用，请遵守YouTube服务条款和相关法律法规。
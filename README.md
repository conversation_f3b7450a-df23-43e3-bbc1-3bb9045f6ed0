# AI音频字幕工具

一个基于火山引擎API的音频字幕工具，支持两种模式：
1. 直接上传音频文件提取字幕
2. 输入文本生成语音并自动提取字幕

## 功能特点

### 核心功能
- 🎙️ **双模式支持**
  - 模式1：上传音频文件直接提取字幕
  - 模式2：输入文本→生成语音→自动提取字幕
- 📝 **高质量语音合成**（火山引擎长文本TTS）
  - 支持多种音色选择（通用、专业、童声、方言等）
  - 可调节语速、音量、音调
  - 支持长达10万字符的文本
  - 异步合成，适合长文本场景
- 🌍 **精准语音识别**（火山引擎语音识别）
  - 支持中文普通话及多种方言（粤语、吴语、闽南语等）
  - 支持英语、日语、韩语等多种语言
  - 可选择识别类型（说话/唱歌/自动）
- 📄 **标准字幕输出**
  - 生成标准 SRT 格式字幕
  - 可直接导入剪映、Premiere 等视频编辑软件
  - 支持时间轴精确到毫秒

### 高级功能
- 自定义每行字数和每屏行数
- 自动添加标点符号
- 中文数字转阿拉伯数字
- 实时音频播放
- 批量下载（音频+字幕）

## 安装步骤

1. 克隆项目
   ```bash
   git clone <repository_url>
   cd jianyingdraft
   ```

2. 安装依赖
   ```bash
   pip install -r requirements.txt
   ```

3. 配置 API
   - 复制 `.env.example` 为 `.env`
   - 填入你的火山引擎 API 配置：
     ```
     VOLCANO_APP_ID=your_volcano_app_id
     VOLCANO_ACCESS_TOKEN=your_volcano_access_token
     ```

## 使用方法

1. 启动应用
   ```bash
   streamlit run app.py
   ```

2. 在浏览器中打开 http://localhost:8501

3. 选择使用模式：
   - **模式1：上传音频提取字幕**
     - 上传音频文件（MP3、WAV、M4A等）
     - 调整字幕设置
     - 点击"开始转换"
     - 下载生成的SRT字幕
   
   - **模式2：文本生成语音并提取字幕**
     - 输入要转换的文本（最多10万字符）
     - 选择音色和语音参数
     - 点击"生成语音并提取字幕"
     - 等待异步合成完成（几分钟到几十分钟）
     - 试听生成的音频
     - 下载音频和字幕文件

## 项目结构

```
jianyingdraft/
├── app.py              # Streamlit 主应用
├── volcano_api.py      # 火山引擎 API 封装（含语音识别和TTS）
├── requirements.txt    # Python 依赖
├── .env.example       # 配置文件示例
└── README.md          # 项目说明
```

## API 说明

### 火山引擎语音识别 API
- 用于音频转文字和字幕生成
- 支持异步处理长音频
- 提供精确的时间戳信息

### 火山引擎长文本语音合成 API
- 用于将文本转换为高质量语音
- 支持最多10万字符的文本
- 异步合成，适合长文本场景
- 支持多种专业音色

## 支持的格式

### 音频格式
- 输入：MP3、WAV、M4A、WEBM、MP4、MPEG、MPGA
- 输出：MP3、WAV、PCM、OGG_OPUS

### 字幕格式
- SRT（SubRip Text）- 最通用的字幕格式

## 音色列表

- **通用女声**：通用、清新、专业温暖、专业知性
- **通用男声**：通用、活力、专业沉稳、专业青年
- **童声**：男孩、女孩
- **方言**：粤语女声、粤语男声

## 注意事项

1. **API 限制**
   - 长文本合成：单次最多10万字符
   - 异步合成：返回时间从几分钟到几十分钟不等
   - 合成结果保留7天

2. **性能建议**
   - 长文本合成适合批量处理场景
   - 对时效性要求高的场景建议使用短文本接口
   - 建议使用清晰的音频文件以获得更好的识别效果

3. **数据安全**
   - 所有处理都通过火山引擎API完成
   - 临时文件会自动清理
   - 不会保存用户数据

## 常见问题

**Q: 文本转语音需要多长时间？**
A: 火山引擎长文本合成为异步服务，通常需要几分钟到几十分钟，最长可能需要3小时。

**Q: 生成的字幕时间不准确怎么办？**
A: 确保上传的音频质量清晰，避免背景噪音过大。

**Q: 支持哪些语言？**
A: 语音识别支持中文（含方言）、英语、日语、韩语等；语音合成主要支持中文和部分方言。

## 技术栈

- Python 3.8+
- Streamlit - Web界面框架
- 火山引擎语音识别 API
- 火山引擎长文本语音合成 API
- python-dotenv - 环境变量管理

## 许可证

MIT License
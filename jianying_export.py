import os
import json
from typing import List, Dict, Optional
from datetime import datetime
from database import Database
from file_manager import FileManager
import pyJianYingDraft as draft
from pyJianYingDraft import (
    Script_file, Draft_folder,
    Video_segment, Audio_segment, Text_segment,
    Video_material, Audio_material,
    Track_type, Font_type, 
    Intro_type, Outro_type,
    Text_style, Clip_settings,
    trange, SEC, Timerange
)

class JianyingExporter:
    def __init__(self, jianying_drafts_path: str):
        """
        初始化剪映导出器
        Args:
            jianying_drafts_path: 剪映草稿文件夹路径，如 "/Users/<USER>/Library/Containers/com.lemon.draft/Drafts"
        """
        self.drafts_path = jianying_drafts_path
        self.draft_folder = Draft_folder(jianying_drafts_path)
        self.db = Database()
        self.file_manager = FileManager()
    
    def create_draft_from_project(self, project_id: int, draft_name: Optional[str] = None) -> bool:
        """
        从项目创建剪映草稿
        Args:
            project_id: 项目ID
            draft_name: 草稿名称，不指定则自动生成
        Returns:
            是否创建成功
        """
        try:
            # 获取项目详情
            project = self.db.get_project_details(project_id)
            
            if not project:
                print(f"项目 {project_id} 不存在")
                return False
            
            # 生成草稿名称
            if not draft_name:
                draft_name = f"{project['name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 检查必要的文件
            if not project['audio_files']:
                print("项目中没有音频文件")
                return False
            
            if not project['subtitles']:
                print("项目中没有字幕文件")
                return False
            
            if not project['images']:
                print("项目中没有图片文件")
                return False
            
            # 创建新草稿
            print(f"正在创建剪映草稿: {draft_name}")
            
            # 创建Script对象
            script = Script_file(1920, 1080)  # 默认1080p
            
            # 1. 添加音频轨道和音频
            print("添加音频...")
            script.add_track(Track_type.audio, "主音频")
            
            audio_info = project['audio_files'][0]  # 使用第一个音频文件
            audio_path = audio_info['file_path']
            
            if not os.path.exists(audio_path):
                print(f"音频文件不存在: {audio_path}")
                return False
            
            # 创建音频素材
            audio_material = Audio_material(audio_path)
            
            # 添加音频片段，使用整个音频
            audio_segment = Audio_segment(audio_material, trange(0, audio_material.duration))
            script.add_segment(audio_segment, "主音频")
            
            # 获取音频时长
            audio_duration = audio_material.duration
            
            # 2. 添加字幕轨道和导入字幕
            print("添加字幕...")
            script.add_track(Track_type.text, "字幕")
            
            subtitle_info = project['subtitles'][0]  # 使用第一个字幕文件
            srt_path = subtitle_info['srt_file_path']
            
            if not os.path.exists(srt_path):
                # 如果SRT文件不存在，从数据库内容创建
                print("SRT文件不存在，从数据库内容创建...")
                srt_content = subtitle_info['srt_content']
                if srt_content:
                    # 创建临时SRT文件
                    temp_srt_path = self.file_manager.get_temp_file_path('.srt')
                    with open(temp_srt_path, 'w', encoding='utf-8') as f:
                        f.write(srt_content)
                    srt_path = temp_srt_path
                else:
                    print("没有可用的字幕内容")
                    return False
            
            # 导入SRT字幕，设置样式
            script.import_srt(
                srt_path,
                track_name="字幕",
                text_style=Text_style(
                    size=8.0,  # 字体大小
                    color=(1.0, 1.0, 1.0),  # 白色
                    bold=True,  # 粗体
                    align=1,  # 居中对齐
                ),
                clip_settings=Clip_settings(
                    transform_y=-0.7  # 放在屏幕下方
                )
            )
            
            # 3. 添加图片轨道和图片
            print("添加图片...")
            script.add_track(Track_type.video, "分镜图片")

            # 获取成功的图片，按场景排序
            success_images = [img for img in project['images'] if img['status'] == 'success']
            success_images.sort(key=lambda x: x['scene_index'])

            if not success_images:
                print("没有成功生成的图片")
                return False

            print(f"找到 {len(success_images)} 张成功生成的图片")

            # 为每个图片创建片段
            for i, image_info in enumerate(success_images):
                image_path = image_info['file_path']

                if not os.path.exists(image_path):
                    print(f"图片文件不存在: {image_path}")
                    continue

                print(f"处理图片 {i+1}: {image_path}")

                # 计算图片的时间范围（转换为微秒）
                start_time = int(image_info['start_time'] * 1000000)  # 转换为微秒
                end_time = int(image_info['end_time'] * 1000000)      # 转换为微秒

                # 确保不超过音频时长
                if start_time >= audio_duration:
                    print(f"图片 {i+1} 开始时间超出音频时长，跳过")
                    continue

                if end_time > audio_duration:
                    end_time = audio_duration
                    print(f"图片 {i+1} 结束时间超出音频时长，已调整")

                duration = end_time - start_time

                if duration <= 0:
                    print(f"图片 {i+1} 持续时间无效，跳过")
                    continue

                try:
                    # 创建图片素材
                    image_material = Video_material(image_path)
                    print(f"图片素材创建成功: {image_material.duration / 1000000:.2f}s")

                    # 创建图片片段 - 使用trange函数确保时间格式正确
                    image_segment = Video_segment(
                        image_material,
                        trange(start_time, duration),  # 使用trange函数
                        clip_settings=Clip_settings(
                            scale_x=1.0,  # 保持原始比例
                            scale_y=1.0,
                            transform_x=0.0,  # 居中显示
                            transform_y=0.0   # 居中显示
                        )
                    )

                    # 添加淡入淡出效果
                    if i == 0:  # 第一张图片淡入
                        image_segment.add_animation(Intro_type.淡入)
                        print("添加淡入效果")
                    if i == len(success_images) - 1:  # 最后一张图片淡出
                        image_segment.add_animation(Outro_type.淡出)
                        print("添加淡出效果")

                    # 添加到轨道
                    script.add_segment(image_segment, "分镜图片")
                    print(f"✅ 成功添加图片 {i+1}/{len(success_images)}: 场景{image_info['scene_index']} ({image_info['start_time']:.1f}s - {image_info['end_time']:.1f}s)")

                except Exception as e:
                    print(f"❌ 添加图片 {i+1} 时出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    continue
            
            # 4. 保存草稿到剪映
            print("保存草稿...")

            # 创建草稿文件夹
            draft_dir = os.path.join(self.drafts_path, draft_name)
            os.makedirs(draft_dir, exist_ok=True)

            # 保存draft_content.json
            draft_content_path = os.path.join(draft_dir, "draft_content.json")
            script.dump(draft_content_path)

            # 验证生成的草稿文件
            if os.path.exists(draft_content_path):
                file_size = os.path.getsize(draft_content_path)
                print(f"草稿文件已生成: {draft_content_path} ({file_size} bytes)")

                # 检查草稿内容
                try:
                    with open(draft_content_path, 'r', encoding='utf-8') as f:
                        draft_data = json.load(f)

                    # 统计轨道和片段数量
                    tracks = draft_data.get('tracks', [])
                    video_tracks = [t for t in tracks if t.get('type') == 'video']
                    audio_tracks = [t for t in tracks if t.get('type') == 'audio']
                    text_tracks = [t for t in tracks if t.get('type') == 'text']

                    print(f"草稿包含: {len(video_tracks)} 个视频轨道, {len(audio_tracks)} 个音频轨道, {len(text_tracks)} 个文本轨道")

                    # 统计片段数量
                    total_segments = 0
                    for track in tracks:
                        segments = track.get('segments', [])
                        total_segments += len(segments)
                        if track.get('type') == 'video':
                            print(f"视频轨道 '{track.get('attribute', {}).get('name', 'unnamed')}' 包含 {len(segments)} 个片段")

                    print(f"总共 {total_segments} 个片段")

                except Exception as e:
                    print(f"验证草稿文件时出错: {str(e)}")

            # 创建draft_meta_info.json
            meta_info = {
                "draft_id": str(os.urandom(16).hex()),
                "draft_name": draft_name,
                "draft_folder": draft_name,
                "create_time": int(datetime.now().timestamp() * 1000),
                "update_time": int(datetime.now().timestamp() * 1000),
                "duration": int(audio_duration / 1000),  # 转换为毫秒
                "cover_path": "",
                "fps": 30,
                "resolution": {
                    "width": 1920,
                    "height": 1080
                }
            }

            meta_info_path = os.path.join(draft_dir, "draft_meta_info.json")
            with open(meta_info_path, 'w', encoding='utf-8') as f:
                json.dump(meta_info, f, ensure_ascii=False, indent=2)

            print(f"✅ 剪映草稿创建成功: {draft_name}")
            print(f"草稿位置: {draft_dir}")
            print("📝 草稿包含:")
            print(f"   - 音频: {len(project['audio_files'])} 个文件")
            print(f"   - 字幕: {len(project['subtitles'])} 个文件")
            print(f"   - 图片: {len(success_images)} 张图片")
            print("🎬 请打开剪映查看草稿")

            # 更新数据库记录
            self.db.conn.execute(
                "UPDATE projects SET jianying_draft_path = ? WHERE id = ?",
                (draft_dir, project_id)
            )
            self.db.conn.commit()

            return True
            
        except Exception as e:
            print(f"创建剪映草稿时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def export_video(self, draft_name: str, output_path: str, resolution: str = "1080p", framerate: int = 30):
        """
        导出剪映草稿为视频文件
        Args:
            draft_name: 草稿名称
            output_path: 输出视频路径
            resolution: 分辨率 (720p/1080p/4k)
            framerate: 帧率 (24/30/60)
        """
        try:
            from pyJianYingDraft import Jianying_controller, Export_resolution, Export_framerate
            
            # 映射分辨率
            res_map = {
                "720p": Export_resolution.RES_720P,
                "1080p": Export_resolution.RES_1080P,
                "4k": Export_resolution.RES_4K
            }
            
            # 映射帧率
            fr_map = {
                24: Export_framerate.FR_24,
                30: Export_framerate.FR_30,
                60: Export_framerate.FR_60
            }
            
            print(f"准备导出草稿: {draft_name}")
            print(f"输出路径: {output_path}")
            print(f"分辨率: {resolution}, 帧率: {framerate}fps")
            
            # 创建控制器
            ctrl = Jianying_controller()
            
            # 导出草稿
            ctrl.export_draft(
                draft_name,
                output_path,
                resolution=res_map.get(resolution, Export_resolution.RES_1080P),
                framerate=fr_map.get(framerate, Export_framerate.FR_30)
            )
            
            print(f"✅ 视频导出成功: {output_path}")
            return True
            
        except Exception as e:
            print(f"导出视频时出错: {str(e)}")
            return False
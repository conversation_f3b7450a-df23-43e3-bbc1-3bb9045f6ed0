# 视频生成器使用说明

## 功能介绍

这个脚本可以读取指定文件夹下的图片，根据输入的文案生成语音和字幕，最后将图片、语音和字幕合成为视频。

## 安装步骤

### 1. 安装Python依赖

```bash
pip install moviepy Pillow numpy imageio-ffmpeg
```

或者使用requirements.txt：

```bash
pip install -r requirements.txt
```

### 2. 安装FFmpeg（必需）

**macOS:**
```bash
brew install ffmpeg
```

**Windows:**
从 https://ffmpeg.org/download.html 下载并安装

**Linux:**
```bash
sudo apt-get install ffmpeg  # Ubuntu/Debian
sudo yum install ffmpeg      # CentOS/RHEL
```

### 3. 配置API凭证

在项目根目录创建或编辑`.env`文件：

```
VOLCANO_APP_ID=你的APP_ID
VOLCANO_ACCESS_TOKEN=你的ACCESS_TOKEN
```

## 使用方法

### 1. 准备图片

将要使用的图片放入"矩阵产品"文件夹（如果文件夹不存在则创建）：

```bash
mkdir 矩阵产品
# 将图片复制到该文件夹
```

支持的图片格式：
- JPG/JPEG
- PNG
- BMP
- GIF（仅第一帧）

### 2. 运行测试脚本（推荐）

首先运行测试脚本检查环境是否正确配置：

```bash
python test_video_generator.py
```

确保所有测试都通过。

### 3. 运行主程序

```bash
python generate_video_from_images.py
```

### 4. 输入文案

程序运行后会提示输入配音文案：
- 输入你想要的文案内容
- 支持多行输入
- 输入完成后连续按两次回车结束输入

### 5. 等待处理

程序会自动：
1. 调用火山引擎TTS生成语音
2. 获取字幕时间轴
3. 处理图片（自动调整为1920x1080）
4. 合成视频（添加转场效果）
5. 添加字幕
6. 输出最终视频

## 输出文件

- `output_video.mp4` - 生成的视频文件
- `output_video.srt` - 字幕文件（可用于后期编辑）

## 自定义选项

在`generate_video_from_images.py`中可以修改的参数：

```python
# 音色选择（第379行附近）
voice_type = "BV701_streaming"  # 可选其他音色

# 输出文件名（第378行附近）
output_video = "output_video.mp4"

# 转场时长（create_video_with_subtitles函数参数）
transition_duration = 0.5  # 秒
```

### 可用音色列表

- `BV001_streaming` - 通用女声
- `BV002` - 通用女声-清新
- `BV701_streaming` - 专业女声-温暖（默认）
- `BV702_streaming` - 专业女声-知性
- `BV003_streaming` - 通用男声
- `BV004` - 通用男声-活力
- `BV703_streaming` - 专业男声-沉稳
- `BV704_streaming` - 专业男声-青年
- `BV705_streaming` - 儿童音-男孩
- `BV706_streaming` - 儿童音-女孩

## 常见问题

### 1. PIL.Image.ANTIALIAS错误

已在代码中处理，会自动使用兼容的LANCZOS替代。

### 2. 字体显示问题

程序会自动尝试多个字体，如果都失败会生成无字幕版本。

### 3. FFmpeg未找到

请确保正确安装FFmpeg并添加到系统PATH。

### 4. 视频渲染失败

程序会自动尝试备用渲染设置（mpeg4编码）。

### 5. 内存不足

如果图片过多或分辨率过高，可能导致内存不足。建议：
- 减少图片数量
- 降低图片分辨率
- 关闭其他程序释放内存

## 高级用法

### 修改每张图片显示时长

在调用`create_video_with_subtitles`时指定`image_duration`参数：

```python
generator.create_video_with_subtitles(
    images=images,
    audio_path=audio_path,
    subtitles=subtitles,
    output_path=output_video,
    image_duration=3.0,  # 每张图片显示3秒
    transition_duration=0.5
)
```

### 批量处理

可以修改主函数，循环处理多个文件夹：

```python
folders = ["矩阵产品1", "矩阵产品2", "矩阵产品3"]
for folder in folders:
    # 处理每个文件夹...
```

## 注意事项

1. 确保有稳定的网络连接（TTS需要调用API）
2. 长文本可能需要较长处理时间
3. 生成的视频文件可能较大，确保有足够磁盘空间
4. 图片会按文件名排序，建议使用数字命名（如：01.jpg, 02.jpg等）
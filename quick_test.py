#!/usr/bin/env python3
"""
快速测试视频生成功能
创建测试图片并生成简短视频
"""

import os
import tempfile
from PIL import Image
from dotenv import load_dotenv
from generate_video_from_images import VideoGenerator

def create_test_images(folder_path, count=3):
    """创建测试图片"""
    os.makedirs(folder_path, exist_ok=True)
    
    colors = ['red', 'green', 'blue']
    image_paths = []
    
    for i in range(count):
        color = colors[i % len(colors)]
        img = Image.new('RGB', (1920, 1080), color=color)
        
        # 在图片上添加文字（使用PIL的基本功能）
        from PIL import ImageDraw
        draw = ImageDraw.Draw(img)
        text = f"Image {i+1}"
        # 简单的文字，不依赖字体
        draw.text((960, 540), text, fill='white', anchor='mm')
        
        img_path = os.path.join(folder_path, f"test_{i+1:02d}.png")
        img.save(img_path)
        image_paths.append(img_path)
        print(f"创建测试图片: {img_path}")
    
    return image_paths

def main():
    """运行快速测试"""
    print("=== 快速测试视频生成功能 ===\n")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API凭证
    app_id = os.getenv('VOLCANO_APP_ID')
    access_token = os.getenv('VOLCANO_ACCESS_TOKEN')
    
    if not app_id or not access_token:
        print("错误：请在.env文件中设置VOLCANO_APP_ID和VOLCANO_ACCESS_TOKEN")
        return
    
    # 创建测试文件夹
    test_folder = "测试图片"
    
    try:
        # 1. 创建测试图片
        print("1. 创建测试图片...")
        images = create_test_images(test_folder, count=3)
        
        # 2. 初始化生成器
        print("\n2. 初始化视频生成器...")
        generator = VideoGenerator(app_id, access_token)
        
        # 3. 测试文案
        test_text = "这是第一张图片，展示红色背景。这是第二张图片，展示绿色背景。这是第三张图片，展示蓝色背景。"
        print(f"\n3. 测试文案: {test_text}")
        
        # 4. 生成语音和字幕
        print("\n4. 生成语音和字幕...")
        result = generator.generate_speech_and_subtitles(test_text, voice_type="BV701_streaming")
        
        if not result:
            print("生成语音失败")
            return
        
        audio_path, subtitles = result
        print(f"✓ 音频生成成功")
        print(f"✓ 获取到 {len(subtitles)} 条字幕")
        
        # 5. 保存字幕
        srt_path = "test_output.srt"
        generator.save_srt_file(subtitles, srt_path)
        
        # 6. 创建视频
        print("\n5. 创建视频...")
        output_path = "test_output.mp4"
        generator.create_video_with_subtitles(
            images=images,
            audio_path=audio_path,
            subtitles=subtitles,
            output_path=output_path,
            transition_duration=0.5
        )
        
        print(f"\n✅ 测试完成！")
        print(f"输出文件：")
        print(f"  - 视频: {output_path}")
        print(f"  - 字幕: {srt_path}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        generator.cleanup_temp_files()
        
        # 询问是否删除测试文件
        response = input("\n是否删除测试文件？(y/n): ")
        if response.lower() == 'y':
            import shutil
            if os.path.exists(test_folder):
                shutil.rmtree(test_folder)
                print(f"已删除 {test_folder}")
            for f in ['test_output.mp4', 'test_output.srt']:
                if os.path.exists(f):
                    os.remove(f)
                    print(f"已删除 {f}")

if __name__ == "__main__":
    main()
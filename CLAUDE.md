# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI-powered audio subtitle tool that integrates with Volcano Engine's APIs. The application provides two main modes:
1. **Audio Upload Mode**: Upload audio files and extract subtitles using speech recognition
2. **Text-to-Speech Mode**: Input text, generate speech audio, and automatically create subtitles

The project also includes AI-powered scene generation, JianYing video editor integration, and supports multiple languages including Chinese dialects.

## Development Commands

### Running the Application
```bash
# Main application
streamlit run app.py
```

### Environment Setup
1. Copy `.env.example` to `.env`
2. Add your Volcano Engine credentials:
   - `VOLCANO_APP_ID`
   - `VOLCANO_ACCESS_TOKEN`

### Testing
Currently, there is no formal testing framework. To test custom prompt functionality:
```bash
python test_custom_prompt.py
```

## Architecture

### Core Components

1. **app.py** - Main Streamlit application handling user interface and workflow orchestration
2. **volcano_api.py** - Wrapper for Volcano Engine APIs (TTS, ASR, AI prompts, image generation)
3. **database.py** - Thread-safe SQLite database management for projects, audio files, subtitles, and AI prompts
4. **file_manager.py** - Structured file operations and project directory management
5. **workflow_audio.py** - Audio processing workflow implementation
6. **jianying_export.py** - JianYing draft export functionality for video editing integration

### Data Flow

1. User inputs text or uploads audio → Creates project in database
2. Text mode: Generate speech via TTS → Save audio file
3. Audio processed through ASR → Generate subtitles
4. Optional: Generate AI scene descriptions → Create images
5. Export to JianYing draft format with audio, subtitles, and images

### Key Design Patterns

- **Async Operations**: Long-running API calls use asyncio for non-blocking execution
- **Thread-Safe Database**: Custom SQLite wrapper ensures safe concurrent access
- **Structured File Management**: Projects organized in `projects/` directory with consistent structure
- **Session State Management**: Streamlit session state tracks current project and user inputs

## Important Considerations

1. **API Rate Limits**: Volcano Engine APIs have rate limits - handle errors appropriately
2. **File Storage**: Generated files stored in `projects/` directory - ensure adequate disk space
3. **Database Migrations**: When modifying database schema, preserve existing data (no data loss allowed per user instructions)
4. **Language Support**: System supports Chinese (multiple dialects), English, Japanese, Korean - ensure proper encoding
5. **PySide6 Migration**: Recent commits indicate preparation for PySide6 - consider compatibility when making UI changes

## Custom Prompt System

The application includes a sophisticated custom AI prompt system for scene generation:
- Users can define templates with placeholders like `{theme}`, `{style}`, `{emotion}`
- Templates support multiple styles (anime, commercial, documentary)
- Prompts stored in database and linked to projects
- See `自定义提示词使用说明.md` for user documentation

## JianYing Integration

The project exports to JianYing draft format:
- Generates JSON structure compatible with JianYing editor
- Includes audio tracks, subtitle tracks, and image tracks
- Supports transitions between scenes
- See `jianying_export.py` for implementation details
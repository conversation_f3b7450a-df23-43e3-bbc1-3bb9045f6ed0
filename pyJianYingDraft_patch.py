"""
macOS 上使用 pyJianYingDraft 的补丁
避免导入依赖 Windows COM 的 jianying_controller 模块
"""
import sys
import os

# 在导入 pyJianYingDraft 之前，先移除有问题的导入
def patch_pyJianYingDraft():
    # 获取 pyJianYingDraft 的路径
    import site
    for path in site.getsitepackages():
        if 'site-packages' in path:
            init_file = os.path.join(path, 'pyJianYingDraft', '__init__.py')
            if os.path.exists(init_file):
                # 读取文件内容
                with open(init_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否需要修补
                if 'from .jianying_controller import' in content and '# PATCHED' not in content:
                    # 注释掉有问题的导入
                    content = content.replace(
                        'from .jianying_controller import Jianying_controller, Export_resolution, Export_framerate',
                        '# PATCHED: Commented out for macOS compatibility\n# from .jianying_controller import Jianying_controller, Export_resolution, Export_framerate'
                    )
                    
                    # 写回文件
                    with open(init_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    print("✅ pyJianYingDraft 已修补，现在可以在 macOS 上使用")
                    return True
                elif '# PATCHED' in content:
                    print("✅ pyJianYingDraft 已经修补过了")
                    return True
    
    print("❌ 未找到 pyJianYingDraft 安装路径")
    return False

if __name__ == "__main__":
    patch_pyJianYingDraft()
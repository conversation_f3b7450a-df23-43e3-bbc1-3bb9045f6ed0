import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Optional

class FileManager:
    def __init__(self, base_dir: str = "projects"):
        self.base_dir = Path(base_dir)
        self.ensure_base_directories()
    
    def ensure_base_directories(self):
        """确保基础目录结构存在"""
        # 创建基础目录
        self.base_dir.mkdir(exist_ok=True)
        
        # 创建临时文件目录
        temp_dir = self.base_dir / "temp"
        temp_dir.mkdir(exist_ok=True)
    
    def create_project_directory(self, project_id: int) -> Path:
        """为项目创建目录结构"""
        # 创建项目主目录
        project_dir = self.base_dir / f"project_{project_id}"
        project_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        subdirs = ['audio', 'subtitles', 'prompts', 'images', 'videos']
        for subdir in subdirs:
            (project_dir / subdir).mkdir(exist_ok=True)
        
        return project_dir
    
    def get_project_directory(self, project_id: int) -> Path:
        """获取项目目录路径"""
        return self.base_dir / f"project_{project_id}"
    
    def save_audio_file(self, project_id: int, source_path: str, 
                       file_name: Optional[str] = None) -> str:
        """保存音频文件到项目目录"""
        project_dir = self.create_project_directory(project_id)
        audio_dir = project_dir / "audio"
        
        if file_name is None:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            ext = os.path.splitext(source_path)[1]
            file_name = f"audio_{timestamp}{ext}"
        
        dest_path = audio_dir / file_name
        
        # 复制文件
        shutil.copy2(source_path, dest_path)
        
        return str(dest_path)
    
    def save_subtitle_file(self, project_id: int, content: str,
                          file_type: str = "srt") -> str:
        """保存字幕文件"""
        project_dir = self.create_project_directory(project_id)
        subtitle_dir = project_dir / "subtitles"
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"subtitle_{timestamp}.{file_type}"
        file_path = subtitle_dir / file_name
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return str(file_path)
    
    def save_prompt_file(self, project_id: int, content: str) -> str:
        """保存AI提示词文件"""
        project_dir = self.create_project_directory(project_id)
        prompt_dir = project_dir / "prompts"
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"prompts_{timestamp}.json"
        file_path = prompt_dir / file_name
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return str(file_path)
    
    def save_image_file(self, project_id: int, source_path: str,
                       scene_index: int, start_ms: int, end_ms: int) -> str:
        """保存图片文件到项目目录"""
        project_dir = self.create_project_directory(project_id)
        image_dir = project_dir / "images"
        
        # 生成文件名
        file_name = f"scene_{scene_index:03d}_{start_ms}_{end_ms}.png"
        dest_path = image_dir / file_name
        
        # 复制文件
        shutil.copy2(source_path, dest_path)
        
        return str(dest_path)
    
    def save_video_file(self, project_id: int, source_path: str,
                       file_name: Optional[str] = None) -> str:
        """保存视频文件到项目目录"""
        project_dir = self.create_project_directory(project_id)
        video_dir = project_dir / "videos"
        
        if file_name is None:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            ext = os.path.splitext(source_path)[1]
            file_name = f"video_{timestamp}{ext}"
        
        dest_path = video_dir / file_name
        
        # 复制文件
        shutil.copy2(source_path, dest_path)
        
        return str(dest_path)
    
    def get_temp_file_path(self, suffix: str = "") -> str:
        """获取临时文件路径"""
        temp_dir = self.base_dir / "temp"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        file_name = f"temp_{timestamp}{suffix}"
        return str(temp_dir / file_name)
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        temp_dir = self.base_dir / "temp"
        if temp_dir.exists():
            for file_path in temp_dir.iterdir():
                if file_path.is_file():
                    # 删除超过24小时的临时文件
                    file_age = datetime.now().timestamp() - file_path.stat().st_mtime
                    if file_age > 86400:  # 24小时
                        file_path.unlink()
    
    def get_project_files(self, project_id: int) -> dict:
        """获取项目的所有文件"""
        project_dir = self.get_project_directory(project_id)
        
        if not project_dir.exists():
            return {}
        
        files = {
            'audio': [],
            'subtitles': [],
            'prompts': [],
            'images': [],
            'videos': []
        }
        
        for category in files.keys():
            category_dir = project_dir / category
            if category_dir.exists():
                files[category] = [str(f) for f in category_dir.iterdir() if f.is_file()]
        
        return files
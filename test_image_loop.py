#!/usr/bin/env python3
"""
测试图片循环功能
创建少量测试图片，生成较长的视频，验证图片是否正确循环
"""

import os
import tempfile
from PIL import Image, ImageDraw, ImageFont
from dotenv import load_dotenv
from generate_video_from_images_v2 import VideoGeneratorV2

def create_numbered_test_images(folder_path, count=3):
    """创建带编号的测试图片"""
    os.makedirs(folder_path, exist_ok=True)
    
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255)]  # 红绿蓝
    image_paths = []
    
    for i in range(count):
        color = colors[i % len(colors)]
        img = Image.new('RGB', (1920, 1080), color)
        
        # 在图片上添加大号数字
        draw = ImageDraw.Draw(img)
        
        # 尝试使用大字体
        try:
            # 尝试加载系统字体
            font = None
            for size in [200, 150, 100, 50]:
                try:
                    if os.path.exists("/System/Library/Fonts/Helvetica.ttc"):
                        font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", size)
                    else:
                        font = ImageFont.load_default()
                    break
                except:
                    continue
            
            if font is None:
                font = ImageFont.load_default()
                
            # 绘制数字
            text = str(i + 1)
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (1920 - text_width) // 2
            y = (1080 - text_height) // 2
            
            # 白色数字，黑色描边
            for offset in [(-2, -2), (-2, 2), (2, -2), (2, 2)]:
                draw.text((x + offset[0], y + offset[1]), text, fill=(0, 0, 0), font=font)
            draw.text((x, y), text, fill=(255, 255, 255), font=font)
            
        except Exception as e:
            print(f"绘制数字失败: {e}")
        
        img_path = os.path.join(folder_path, f"test_{i+1:02d}.png")
        img.save(img_path)
        image_paths.append(img_path)
        print(f"创建测试图片: {img_path}")
    
    return image_paths

def main():
    """测试图片循环功能"""
    print("=== 测试图片循环功能 ===\n")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API凭证
    app_id = os.getenv('VOLCANO_APP_ID')
    access_token = os.getenv('VOLCANO_ACCESS_TOKEN')
    
    if not app_id or not access_token:
        print("错误：请在.env文件中设置VOLCANO_APP_ID和VOLCANO_ACCESS_TOKEN")
        return
    
    # 创建测试文件夹
    test_folder = "测试循环图片"
    
    try:
        # 1. 创建少量测试图片（3张）
        print("1. 创建3张测试图片...")
        images = create_numbered_test_images(test_folder, count=3)
        
        # 2. 初始化生成器
        print("\n2. 初始化视频生成器...")
        generator = VideoGeneratorV2(app_id, access_token)
        
        # 3. 使用较长的测试文案（确保视频时长较长）
        test_text = """
        这是第一段测试文字，用于生成较长的音频。
        我们将测试图片是否能够正确循环显示。
        第一张图片是红色背景，标记为数字1。
        第二张图片是绿色背景，标记为数字2。
        第三张图片是蓝色背景，标记为数字3。
        当所有图片显示完毕后，应该会重新从第一张图片开始循环。
        这样可以确保整个视频时长内都有图片显示，不会出现黑屏。
        让我们看看效果如何。
        """
        
        print(f"\n3. 测试文案（预计生成较长音频）")
        print("文案内容：")
        print(test_text.strip())
        
        # 4. 生成语音和字幕
        print("\n4. 生成语音和字幕...")
        result = generator.generate_speech_and_subtitles(test_text.strip(), voice_type="BV701_streaming")
        
        if not result:
            print("生成语音失败")
            return
        
        audio_path, subtitles = result
        print(f"✓ 音频生成成功")
        print(f"✓ 获取到 {len(subtitles)} 条字幕")
        
        # 5. 保存字幕
        srt_path = "test_loop_output.srt"
        generator.save_srt_file(subtitles, srt_path)
        
        # 6. 创建视频
        print("\n5. 创建视频（测试图片循环）...")
        output_path = "test_loop_output.mp4"
        generator.create_video_with_subtitles(
            images=images,
            audio_path=audio_path,
            subtitles=subtitles,
            output_path=output_path,
            transition_duration=0.5
        )
        
        print(f"\n✅ 测试完成！")
        print(f"输出文件：")
        print(f"  - 视频: {output_path}")
        print(f"  - 字幕: {srt_path}")
        print(f"\n请检查视频，确认：")
        print(f"  1. 图片是否正确循环（1-2-3-1-2-3...）")
        print(f"  2. 整个视频时长内都有图片显示")
        print(f"  3. 没有黑屏出现")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        if 'generator' in locals():
            generator.cleanup_temp_files()

if __name__ == "__main__":
    main()
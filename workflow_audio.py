import streamlit as st
import os
import json
import asyncio
from datetime import datetime
from database import Database
from file_manager import FileManager
from volcano_api import VolcanoSpeechAPI, convert_to_srt, get_ai_prompt_from_subtitles, generate_images_for_scenes

async def run_audio_workflow(project_id: int, audio_file, settings: dict, db: Database, file_manager: FileManager, volcano_api: VolcanoSpeechAPI):
    """处理上传音频文件的工作流程"""
    try:
        # 更新项目状态
        db.update_project_status(project_id, 'processing')
        
        # 步骤1: 保存音频文件
        db.update_progress(project_id, 'save_audio', 'in_progress', 50, '正在保存音频文件...')
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        ext = os.path.splitext(audio_file.name)[1]
        file_name = f"uploaded_{timestamp}{ext}"
        
        # 创建临时文件保存上传的音频
        temp_path = file_manager.get_temp_file_path(ext)
        with open(temp_path, 'wb') as f:
            f.write(audio_file.read())
        
        # 保存到项目目录
        audio_path = file_manager.save_audio_file(project_id, temp_path, file_name)
        
        # 获取文件大小
        file_size = os.path.getsize(audio_path)
        
        # 保存到数据库
        audio_id = db.save_audio_file(
            project_id=project_id,
            file_path=audio_path,
            file_name=file_name,
            file_size=file_size,
            format=ext[1:] if ext else 'unknown'
        )
        
        db.update_progress(project_id, 'save_audio', 'completed', 100, '音频文件保存成功')
        
        # 步骤2: 生成字幕
        db.update_progress(project_id, 'generate_subtitle', 'in_progress', 10, '正在生成字幕...')
        
        # 重置文件指针
        audio_file.seek(0)
        
        # 提交到火山引擎字幕生成API
        submit_result = volcano_api.submit_audio(
            audio_file=audio_file,
            words_per_line=settings.get('words_per_line', 20),
            max_lines=settings.get('max_lines', 2),
            language=settings.get('language', 'zh-CN'),
            use_punc=settings.get('use_punc', True),
            caption_type=settings.get('caption_type', 'speech')
        )
        
        if submit_result.get("code") != 0:
            db.update_progress(project_id, 'generate_subtitle', 'failed', 0, f'字幕生成提交失败: {submit_result.get("message")}')
            db.update_project_status(project_id, 'failed')
            return False
        
        task_id = submit_result.get("id")
        db.update_progress(project_id, 'generate_subtitle', 'in_progress', 50, f'字幕任务已提交，任务ID: {task_id}')
        
        # 查询结果
        result = volcano_api.query_result(task_id, blocking=True)
        
        if result.get("code") != 0:
            db.update_progress(project_id, 'generate_subtitle', 'failed', 0, f'字幕处理失败: {result.get("message")}')
            db.update_project_status(project_id, 'failed')
            return False
        
        # 保存字幕
        utterances = result.get("utterances", [])
        srt_content = convert_to_srt(utterances)
        srt_file_path = file_manager.save_subtitle_file(project_id, srt_content, "srt")
        
        # 保存字幕信息到数据库
        subtitle_id = db.save_subtitle(
            project_id=project_id,
            audio_id=audio_id,
            srt_file_path=srt_file_path,
            srt_content=srt_content,
            json_content=json.dumps(utterances, ensure_ascii=False),
            volcano_task_id=task_id
        )
        
        db.update_progress(project_id, 'generate_subtitle', 'completed', 100, '字幕生成成功')
        
        # 步骤3: 生成AI提示词
        db.update_progress(project_id, 'generate_ai_prompt', 'in_progress', 10, '正在生成AI分镜提示词...')
        
        # 准备字幕数据
        subtitle_data = []
        for utterance in utterances:
            subtitle_data.append({
                "content": utterance['text'],
                "start": int(utterance.get('start_time', utterance.get('begin_time', 0)) * 1000),
                "end": int(utterance.get('end_time', utterance.get('end_time', 0)) * 1000)
            })
        
        # 生成AI提示词
        custom_prompt = settings.get('custom_prompt')
        ai_prompt_result = await get_ai_prompt_from_subtitles(subtitle_data, custom_prompt=custom_prompt)
        
        if not ai_prompt_result:
            db.update_progress(project_id, 'generate_ai_prompt', 'failed', 0, 'AI提示词生成失败')
            db.update_project_status(project_id, 'failed')
            return False
        
        # 保存提示词文件
        prompts_json = json.dumps(ai_prompt_result, ensure_ascii=False, indent=2)
        prompts_file_path = file_manager.save_prompt_file(project_id, prompts_json)
        
        # 保存到数据库
        prompts_with_index = []
        for i, prompt in enumerate(ai_prompt_result):
            prompt['scene_index'] = i + 1
            prompts_with_index.append(prompt)
        
        db.save_ai_prompts(project_id, subtitle_id, prompts_with_index, prompts_file_path)
        
        db.update_progress(project_id, 'generate_ai_prompt', 'completed', 100, f'成功生成{len(ai_prompt_result)}个场景提示词')
        
        # 步骤4: 生成图片
        db.update_progress(project_id, 'generate_images', 'in_progress', 10, f'正在生成{len(ai_prompt_result)}个场景的图片...')
        
        # 获取DMXAPI密钥
        dmx_api_key = os.getenv('DMXAPI_KEY')
        
        # 生成图片
        image_results = await generate_images_for_scenes(
            scenes=ai_prompt_result,
            api_key=dmx_api_key,
            model="flux-schnell"
        )
        
        # 保存图片信息
        success_count = 0
        for result in image_results:
            if result['success']:
                # 保存图片到项目目录
                saved_path = file_manager.save_image_file(
                    project_id,
                    result['local_path'],
                    result['scene_index'],
                    result['start'],
                    result['end']
                )
                
                # 保存到数据库
                db.save_image(
                    project_id=project_id,
                    scene_index=result['scene_index'],
                    start_time=result['start'] / 1000.0,
                    end_time=result['end'] / 1000.0,
                    file_path=saved_path,
                    file_name=os.path.basename(saved_path),
                    image_url=result.get('image_url'),
                    prompt_used=result['prompt'],
                    model_used="flux-schnell",
                    status='success'
                )
                success_count += 1
            else:
                # 记录失败的图片
                db.save_image(
                    project_id=project_id,
                    scene_index=result['scene_index'],
                    start_time=result['start'] / 1000.0,
                    end_time=result['end'] / 1000.0,
                    file_path='',
                    file_name='',
                    prompt_used=result['prompt'],
                    model_used="flux-schnell",
                    status='failed',
                    error_message=result.get('error', '未知错误')
                )
        
        db.update_progress(project_id, 'generate_images', 'completed', 100, 
                          f'图片生成完成！成功 {success_count}/{len(image_results)} 个')
        
        # 更新项目状态
        db.update_project_status(project_id, 'completed')
        
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)
        
        return True
        
    except Exception as e:
        db.update_project_status(project_id, 'failed')
        db.update_progress(project_id, 'generate_images', 'failed', 0, f'发生错误: {str(e)}')
        st.error(f"音频处理工作流程出错: {str(e)}")
        return False
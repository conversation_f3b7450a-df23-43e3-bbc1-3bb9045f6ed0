"""
YouTube视频管理系统数据库模块
扩展现有数据库功能，添加YouTube账号管理和视频上传相关表
"""

import sqlite3
import json
import threading
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from pathlib import Path
import os

logger = logging.getLogger(__name__)

class YouTubeDatabase:
    """YouTube视频管理数据库"""
    
    def __init__(self, db_path: str = "youtube_manager.db"):
        self.db_path = db_path
        self._lock = threading.Lock()
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            try:
                cursor = conn.cursor()
                
                # videos表 - 存储视频信息
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS videos (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id INTEGER,
                    file_path TEXT NOT NULL,
                    title TEXT,
                    description TEXT,
                    tags TEXT,  -- JSON格式
                    category_id TEXT,
                    privacy_status TEXT DEFAULT 'private',  -- private, unlisted, public
                    duration INTEGER,  -- 秒
                    file_size INTEGER,  -- 字节
                    resolution TEXT,  -- 如 1920x1080
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status TEXT DEFAULT 'generated',  -- generated, ready, uploading, uploaded, failed
                    thumbnail_path TEXT,
                    FOREIGN KEY (project_id) REFERENCES projects(id)
                )''')
                
                # youtube_accounts表 - YouTube账号信息
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS youtube_accounts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    account_name TEXT NOT NULL UNIQUE,
                    email TEXT,
                    channel_id TEXT,
                    channel_name TEXT,
                    credentials_path TEXT,  -- OAuth2凭证文件路径
                    token_data TEXT,  -- 加密的token数据
                    is_active INTEGER DEFAULT 1,
                    quota_used INTEGER DEFAULT 0,  -- 已使用配额
                    quota_limit INTEGER DEFAULT 10000,  -- 每日配额限制
                    last_quota_reset TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )''')
                
                # upload_tasks表 - 上传任务队列
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS upload_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id INTEGER NOT NULL,
                    account_id INTEGER NOT NULL,
                    scheduled_time TIMESTAMP,  -- 计划上传时间
                    priority INTEGER DEFAULT 0,  -- 优先级，数字越大越优先
                    status TEXT DEFAULT 'pending',  -- pending, processing, completed, failed, cancelled
                    retry_count INTEGER DEFAULT 0,
                    max_retries INTEGER DEFAULT 3,
                    youtube_video_id TEXT,  -- YouTube返回的视频ID
                    youtube_url TEXT,  -- YouTube视频URL
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    started_at TIMESTAMP,
                    completed_at TIMESTAMP,
                    FOREIGN KEY (video_id) REFERENCES videos(id),
                    FOREIGN KEY (account_id) REFERENCES youtube_accounts(id)
                )''')
                
                # upload_logs表 - 上传日志
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS upload_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id INTEGER,
                    account_id INTEGER,
                    video_id INTEGER,
                    action TEXT NOT NULL,  -- upload_start, upload_progress, upload_complete, upload_failed, etc.
                    message TEXT,
                    details TEXT,  -- JSON格式的详细信息
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (task_id) REFERENCES upload_tasks(id),
                    FOREIGN KEY (account_id) REFERENCES youtube_accounts(id),
                    FOREIGN KEY (video_id) REFERENCES videos(id)
                )''')
                
                # video_metadata表 - 视频元数据模板
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS video_metadata_templates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    template_name TEXT NOT NULL UNIQUE,
                    title_template TEXT,  -- 支持变量如 {project_name}, {date}
                    description_template TEXT,
                    tags_template TEXT,  -- JSON格式
                    category_id TEXT,
                    privacy_status TEXT DEFAULT 'private',
                    is_default INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )''')
                
                # account_video_mapping表 - 账号视频发布记录
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS account_video_mapping (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    account_id INTEGER NOT NULL,
                    video_id INTEGER NOT NULL,
                    youtube_video_id TEXT,
                    youtube_url TEXT,
                    views INTEGER DEFAULT 0,
                    likes INTEGER DEFAULT 0,
                    comments INTEGER DEFAULT 0,
                    published_at TIMESTAMP,
                    last_stats_update TIMESTAMP,
                    FOREIGN KEY (account_id) REFERENCES youtube_accounts(id),
                    FOREIGN KEY (video_id) REFERENCES videos(id),
                    UNIQUE(account_id, video_id)
                )''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_project_id ON videos(project_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_videos_status ON videos(status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_upload_tasks_status ON upload_tasks(status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_upload_tasks_scheduled_time ON upload_tasks(scheduled_time)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_upload_logs_task_id ON upload_logs(task_id)')
                
                conn.commit()
                logger.info("YouTube数据库表初始化完成")
                
            except Exception as e:
                logger.error(f"初始化数据库失败: {str(e)}")
                conn.rollback()
                raise
            finally:
                conn.close()
    
    # ========== 视频管理 ==========
    
    def add_video(self, project_id: int, file_path: str, title: str = None, 
                  description: str = None, tags: List[str] = None, **kwargs) -> int:
        """添加视频记录"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # 获取文件信息
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                else:
                    file_size = 0
                
                cursor.execute('''
                INSERT INTO videos (project_id, file_path, title, description, tags, 
                                   file_size, category_id, privacy_status, duration, 
                                   resolution, thumbnail_path)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    project_id, file_path, title, description,
                    json.dumps(tags) if tags else None,
                    file_size,
                    kwargs.get('category_id'),
                    kwargs.get('privacy_status', 'private'),
                    kwargs.get('duration'),
                    kwargs.get('resolution'),
                    kwargs.get('thumbnail_path')
                ))
                
                video_id = cursor.lastrowid
                conn.commit()
                logger.info(f"添加视频记录: ID={video_id}, 路径={file_path}")
                return video_id
                
            except Exception as e:
                logger.error(f"添加视频失败: {str(e)}")
                conn.rollback()
                raise
            finally:
                conn.close()
    
    def get_video(self, video_id: int) -> Optional[Dict[str, Any]]:
        """获取视频信息"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            try:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM videos WHERE id = ?', (video_id,))
                row = cursor.fetchone()
                if row:
                    video = dict(row)
                    if video.get('tags'):
                        video['tags'] = json.loads(video['tags'])
                    return video
                return None
            finally:
                conn.close()
    
    def get_videos_by_status(self, status: str) -> List[Dict[str, Any]]:
        """根据状态获取视频列表"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            try:
                cursor = conn.cursor()
                cursor.execute('''
                SELECT v.*, p.name as project_name
                FROM videos v
                LEFT JOIN projects p ON v.project_id = p.id
                WHERE v.status = ?
                ORDER BY v.created_at DESC
                ''', (status,))
                videos = []
                for row in cursor.fetchall():
                    video = dict(row)
                    if video.get('tags'):
                        video['tags'] = json.loads(video['tags'])
                    videos.append(video)
                return videos
            finally:
                conn.close()
    
    def update_video_status(self, video_id: int, status: str):
        """更新视频状态"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute('''
                UPDATE videos 
                SET status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                ''', (status, video_id))
                conn.commit()
            finally:
                conn.close()
    
    # ========== YouTube账号管理 ==========
    
    def add_youtube_account(self, account_name: str, email: str = None, 
                           credentials_path: str = None, **kwargs) -> int:
        """添加YouTube账号"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute('''
                INSERT INTO youtube_accounts (account_name, email, credentials_path,
                                            channel_id, channel_name, quota_limit)
                VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    account_name, email, credentials_path,
                    kwargs.get('channel_id'),
                    kwargs.get('channel_name'),
                    kwargs.get('quota_limit', 10000)
                ))
                
                account_id = cursor.lastrowid
                conn.commit()
                logger.info(f"添加YouTube账号: ID={account_id}, 名称={account_name}")
                return account_id
                
            except sqlite3.IntegrityError:
                logger.error(f"账号名称已存在: {account_name}")
                raise ValueError(f"账号名称已存在: {account_name}")
            except Exception as e:
                logger.error(f"添加账号失败: {str(e)}")
                conn.rollback()
                raise
            finally:
                conn.close()
    
    def get_youtube_account(self, account_id: int) -> Optional[Dict[str, Any]]:
        """获取YouTube账号信息"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            try:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM youtube_accounts WHERE id = ?', (account_id,))
                row = cursor.fetchone()
                return dict(row) if row else None
            finally:
                conn.close()
    
    def get_active_youtube_accounts(self) -> List[Dict[str, Any]]:
        """获取所有活跃的YouTube账号"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            try:
                cursor = conn.cursor()
                cursor.execute('''
                SELECT * FROM youtube_accounts 
                WHERE is_active = 1
                ORDER BY account_name
                ''')
                return [dict(row) for row in cursor.fetchall()]
            finally:
                conn.close()
    
    def update_account_token(self, account_id: int, token_data: str):
        """更新账号token"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute('''
                UPDATE youtube_accounts 
                SET token_data = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                ''', (token_data, account_id))
                conn.commit()
            finally:
                conn.close()
    
    def update_account_quota(self, account_id: int, quota_used: int):
        """更新账号配额使用情况"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute('''
                UPDATE youtube_accounts 
                SET quota_used = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                ''', (quota_used, account_id))
                conn.commit()
            finally:
                conn.close()
    
    # ========== 上传任务管理 ==========
    
    def create_upload_task(self, video_id: int, account_id: int, 
                          scheduled_time: datetime = None, priority: int = 0) -> int:
        """创建上传任务"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute('''
                INSERT INTO upload_tasks (video_id, account_id, scheduled_time, priority)
                VALUES (?, ?, ?, ?)
                ''', (video_id, account_id, scheduled_time, priority))
                
                task_id = cursor.lastrowid
                conn.commit()
                logger.info(f"创建上传任务: ID={task_id}, 视频={video_id}, 账号={account_id}")
                return task_id
                
            except Exception as e:
                logger.error(f"创建上传任务失败: {str(e)}")
                conn.rollback()
                raise
            finally:
                conn.close()
    
    def get_pending_upload_tasks(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取待处理的上传任务"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            try:
                cursor = conn.cursor()
                cursor.execute('''
                SELECT t.*, v.file_path, v.title, v.description, v.tags,
                       a.account_name, a.credentials_path, a.token_data
                FROM upload_tasks t
                JOIN videos v ON t.video_id = v.id
                JOIN youtube_accounts a ON t.account_id = a.id
                WHERE t.status = 'pending'
                  AND (t.scheduled_time IS NULL OR t.scheduled_time <= datetime('now'))
                  AND t.retry_count < t.max_retries
                  AND a.is_active = 1
                ORDER BY t.priority DESC, t.created_at ASC
                LIMIT ?
                ''', (limit,))
                
                tasks = []
                for row in cursor.fetchall():
                    task = dict(row)
                    if task.get('tags'):
                        task['tags'] = json.loads(task['tags'])
                    tasks.append(task)
                return tasks
            finally:
                conn.close()
    
    def update_upload_task(self, task_id: int, status: str, **kwargs):
        """更新上传任务状态"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # 构建更新语句
                updates = ['status = ?']
                params = [status]
                
                if 'started_at' in kwargs:
                    updates.append('started_at = ?')
                    params.append(kwargs['started_at'])
                
                if 'completed_at' in kwargs:
                    updates.append('completed_at = ?')
                    params.append(kwargs['completed_at'])
                
                if 'youtube_video_id' in kwargs:
                    updates.append('youtube_video_id = ?')
                    params.append(kwargs['youtube_video_id'])
                
                if 'youtube_url' in kwargs:
                    updates.append('youtube_url = ?')
                    params.append(kwargs['youtube_url'])
                
                if 'error_message' in kwargs:
                    updates.append('error_message = ?')
                    params.append(kwargs['error_message'])
                
                if 'retry_count' in kwargs:
                    updates.append('retry_count = ?')
                    params.append(kwargs['retry_count'])
                
                params.append(task_id)
                
                sql = f"UPDATE upload_tasks SET {', '.join(updates)} WHERE id = ?"
                cursor.execute(sql, params)
                conn.commit()
                
            finally:
                conn.close()
    
    # ========== 上传日志 ==========
    
    def add_upload_log(self, task_id: int, account_id: int, video_id: int,
                      action: str, message: str, details: Dict[str, Any] = None):
        """添加上传日志"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute('''
                INSERT INTO upload_logs (task_id, account_id, video_id, action, message, details)
                VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    task_id, account_id, video_id, action, message,
                    json.dumps(details) if details else None
                ))
                conn.commit()
            except Exception as e:
                logger.error(f"添加上传日志失败: {str(e)}")
                # 日志失败不应该影响主流程
            finally:
                conn.close()
    
    def get_upload_logs(self, task_id: int = None, video_id: int = None, 
                       limit: int = 100) -> List[Dict[str, Any]]:
        """获取上传日志"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            try:
                cursor = conn.cursor()
                
                conditions = []
                params = []
                
                if task_id:
                    conditions.append('task_id = ?')
                    params.append(task_id)
                
                if video_id:
                    conditions.append('video_id = ?')
                    params.append(video_id)
                
                where_clause = f"WHERE {' AND '.join(conditions)}" if conditions else ""
                
                cursor.execute(f'''
                SELECT * FROM upload_logs
                {where_clause}
                ORDER BY created_at DESC
                LIMIT ?
                ''', params + [limit])
                
                logs = []
                for row in cursor.fetchall():
                    log = dict(row)
                    if log.get('details'):
                        log['details'] = json.loads(log['details'])
                    logs.append(log)
                return logs
                
            finally:
                conn.close()
    
    # ========== 统计功能 ==========
    
    def get_upload_statistics(self) -> Dict[str, Any]:
        """获取上传统计信息"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # 总视频数
                cursor.execute('SELECT COUNT(*) FROM videos')
                total_videos = cursor.fetchone()[0]
                
                # 各状态视频数
                cursor.execute('''
                SELECT status, COUNT(*) as count
                FROM videos
                GROUP BY status
                ''')
                video_status_counts = {row[0]: row[1] for row in cursor.fetchall()}
                
                # 总上传任务数
                cursor.execute('SELECT COUNT(*) FROM upload_tasks')
                total_tasks = cursor.fetchone()[0]
                
                # 各状态任务数
                cursor.execute('''
                SELECT status, COUNT(*) as count
                FROM upload_tasks
                GROUP BY status
                ''')
                task_status_counts = {row[0]: row[1] for row in cursor.fetchall()}
                
                # 活跃账号数
                cursor.execute('SELECT COUNT(*) FROM youtube_accounts WHERE is_active = 1')
                active_accounts = cursor.fetchone()[0]
                
                # 今日上传数
                cursor.execute('''
                SELECT COUNT(*) FROM upload_tasks
                WHERE status = 'completed'
                  AND DATE(completed_at) = DATE('now')
                ''')
                today_uploads = cursor.fetchone()[0]
                
                return {
                    'total_videos': total_videos,
                    'video_status_counts': video_status_counts,
                    'total_tasks': total_tasks,
                    'task_status_counts': task_status_counts,
                    'active_accounts': active_accounts,
                    'today_uploads': today_uploads
                }
                
            finally:
                conn.close()
    
    # ========== 视频元数据模板 ==========
    
    def add_metadata_template(self, template_name: str, title_template: str = None,
                            description_template: str = None, tags_template: List[str] = None,
                            **kwargs) -> int:
        """添加视频元数据模板"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute('''
                INSERT INTO video_metadata_templates 
                (template_name, title_template, description_template, tags_template,
                 category_id, privacy_status, is_default)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    template_name, title_template, description_template,
                    json.dumps(tags_template) if tags_template else None,
                    kwargs.get('category_id'),
                    kwargs.get('privacy_status', 'private'),
                    kwargs.get('is_default', 0)
                ))
                
                template_id = cursor.lastrowid
                conn.commit()
                return template_id
                
            except sqlite3.IntegrityError:
                raise ValueError(f"模板名称已存在: {template_name}")
            finally:
                conn.close()
    
    def get_metadata_templates(self) -> List[Dict[str, Any]]:
        """获取所有元数据模板"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            try:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM video_metadata_templates ORDER BY template_name')
                templates = []
                for row in cursor.fetchall():
                    template = dict(row)
                    if template.get('tags_template'):
                        template['tags_template'] = json.loads(template['tags_template'])
                    templates.append(template)
                return templates
            finally:
                conn.close()
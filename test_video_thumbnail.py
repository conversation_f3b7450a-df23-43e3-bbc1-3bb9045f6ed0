#!/usr/bin/env python3
"""
测试视频封面/缩略图功能
"""

import os
import subprocess
from PIL import Image
from dotenv import load_dotenv
from generate_video_from_images_v2 import VideoGeneratorV2

def create_test_images_with_numbers():
    """创建带数字的测试图片，第一张特别醒目"""
    test_folder = "测试封面图片"
    os.makedirs(test_folder, exist_ok=True)
    
    # 创建第一张图片 - 亮黄色背景，大号"1"
    img1 = Image.new('RGB', (1920, 1080), (255, 255, 0))  # 亮黄色
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img1)
    # 画一个大大的"1"
    draw.text((860, 440), "1", fill=(0, 0, 0))
    img1.save(os.path.join(test_folder, "test_01.jpg"))
    
    # 创建第二张图片 - 绿色背景
    img2 = Image.new('RGB', (1920, 1080), (0, 255, 0))
    draw2 = ImageDraw.Draw(img2)
    draw2.text((860, 440), "2", fill=(0, 0, 0))
    img2.save(os.path.join(test_folder, "test_02.jpg"))
    
    # 创建第三张图片 - 蓝色背景
    img3 = Image.new('RGB', (1920, 1080), (0, 0, 255))
    draw3 = ImageDraw.Draw(img3)
    draw3.text((860, 440), "3", fill=(255, 255, 255))
    img3.save(os.path.join(test_folder, "test_03.jpg"))
    
    print("✓ 创建了3张测试图片")
    print("  - test_01.jpg (黄色背景)")
    print("  - test_02.jpg (绿色背景)")
    print("  - test_03.jpg (蓝色背景)")
    
    return test_folder

def check_video_thumbnail(video_path):
    """检查视频的第一帧"""
    print(f"\n检查视频封面: {video_path}")
    
    if not os.path.exists(video_path):
        print("❌ 视频文件不存在")
        return
    
    # 使用ffmpeg提取第一帧
    thumbnail_path = video_path.replace('.mp4', '_first_frame.jpg')
    cmd = [
        'ffmpeg', '-y',
        '-i', video_path,
        '-vframes', '1',  # 只提取一帧
        '-f', 'image2',
        thumbnail_path
    ]
    
    try:
        subprocess.run(cmd, capture_output=True, check=True)
        print(f"✓ 已提取第一帧到: {thumbnail_path}")
        print("  请检查这个图片是否是黄色背景（而不是黑色）")
    except subprocess.CalledProcessError as e:
        print(f"❌ 提取第一帧失败: {e}")

def main():
    print("=== 测试视频封面功能 ===\n")
    
    print("说明：")
    print("1. 第一张图片不会有淡入效果")
    print("2. 视频开始有0.1秒的静态封面帧")
    print("3. 使用-movflags +faststart优化")
    print("4. 第一帧应该是黄色背景，而不是黑色\n")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查是否要运行完整测试
    response = input("是否运行完整测试生成视频？(y/n): ")
    if response.lower() != 'y':
        print("\n跳过视频生成")
        return
    
    app_id = os.getenv('VOLCANO_APP_ID')
    access_token = os.getenv('VOLCANO_ACCESS_TOKEN')
    
    if not app_id or not access_token:
        print("请配置API凭证")
        return
    
    # 创建测试图片
    test_folder = create_test_images_with_numbers()
    
    # 初始化生成器
    generator = VideoGeneratorV2(app_id, access_token)
    
    # 简短的测试文案
    test_text = "测试视频封面功能"
    
    # 生成视频
    try:
        # 读取图片
        images = generator.read_images_from_folder(test_folder)
        
        # 生成语音和字幕
        result = generator.generate_speech_and_subtitles(test_text)
        if not result:
            print("生成语音失败")
            return
        
        audio_path, subtitles = result
        
        # 创建视频
        output_path = "test_thumbnail.mp4"
        generator.create_video_with_subtitles(
            images=images,
            audio_path=audio_path,
            subtitles=subtitles,
            output_path=output_path,
            transition_duration=0.5
        )
        
        # 检查视频封面
        check_video_thumbnail(output_path)
        
        print("\n✅ 测试完成！")
        print("请查看:")
        print("1. test_thumbnail.mp4 - 在文件管理器中查看缩略图")
        print("2. test_thumbnail_first_frame.jpg - 提取的第一帧")
        print("3. test_thumbnail_cover.jpg - 单独保存的封面图")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        generator.cleanup_temp_files()

if __name__ == "__main__":
    main()
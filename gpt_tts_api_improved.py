import http.client
import json
import tempfile
import os
from typing import Optional, Dict, List
import subprocess

class ImprovedGPTTextToSpeechAPI:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.host = "api.gptnb.ai"
        self.endpoint = "/v1/audio/speech"
        
    def text_to_speech(self, 
                      text: str,
                      voice: str = "alloy",
                      model: str = "tts-1",
                      response_format: str = "mp3") -> Optional[Dict]:
        """
        使用GPT API进行文本转语音
        """
        
        print(f"\n{'='*50}")
        print(f"GPT TTS 任务")
        print(f"文本长度: {len(text)} 字符")
        print(f"音色: {voice}")
        print(f"模型: {model}")
        print(f"格式: {response_format}")
        print(f"{'='*50}\n")
        
        try:
            print("1. 创建HTTPS连接...")
            conn = http.client.HTTPSConnection(self.host)
            
            payload = json.dumps({
                "model": model,
                "input": text,
                "voice": voice,
                "response_format": response_format
            })
            
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.api_key}'
            }
            
            print("2. 发送请求到GPT API...")
            conn.request("POST", self.endpoint, payload, headers)
            res = conn.getresponse()
            data = res.read()
            
            if res.status == 200:
                print("✅ 请求成功，保存音频文件...")
                
                # 创建临时文件保存音频
                suffix = f".{response_format}"
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
                temp_path = temp_file.name
                
                with open(temp_path, "wb") as f:
                    f.write(data)
                
                file_size = os.path.getsize(temp_path) / 1024 / 1024
                print(f"✅ 音频生成成功: {temp_path}")
                print(f"文件大小: {file_size:.2f} MB")
                
                conn.close()
                
                return {
                    "audio_path": temp_path,
                    "status": "success",
                    "file_size_mb": file_size,
                    "format": response_format,
                    "voice": voice,
                    "model": model
                }
            else:
                error_msg = data.decode('utf-8')
                print(f"❌ 请求失败，状态码: {res.status}")
                print(f"错误信息: {error_msg}")
                conn.close()
                return None
                
        except Exception as e:
            print(f"❌ 发生错误: {type(e).__name__} - {str(e)}")
            return None
    
    def get_audio_duration(self, audio_path: str) -> float:
        """使用ffprobe获取音频文件的准确时长"""
        try:
            cmd = [
                'ffprobe', '-v', 'error',
                '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1',
                audio_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            duration = float(result.stdout.strip())
            print(f"✅ 获取音频实际时长: {duration:.2f} 秒")
            return duration
        except Exception as e:
            print(f"⚠️ 无法获取音频时长，使用估算值: {e}")
            return None
    
    def text_to_speech_with_improved_subtitles(self,
                                              text: str,
                                              voice: str = "alloy",
                                              model: str = "tts-1",
                                              response_format: str = "mp3") -> Optional[Dict]:
        """
        生成音频并创建改进的字幕时间戳
        """
        
        # 首先生成音频
        result = self.text_to_speech(text, voice, model, response_format)
        
        if not result:
            return None
            
        # 获取实际音频时长
        audio_duration = self.get_audio_duration(result['audio_path'])
        
        print("\n3. 生成改进的字幕时间戳...")
        
        # 分割文本
        sentences = self._split_text_to_sentences(text)
        
        # 计算每个句子的权重（基于字符数）
        sentence_weights = []
        total_chars = 0
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence:
                char_count = len(sentence)
                sentence_weights.append((sentence, char_count))
                total_chars += char_count
        
        # 如果获取到了实际音频时长，使用它；否则使用改进的估算
        if audio_duration:
            total_duration_ms = audio_duration * 1000
        else:
            # 改进的估算方法
            is_chinese = any('\u4e00' <= char <= '\u9fff' for char in text)
            
            # 调整基础语速
            if is_chinese:
                # 中文语速：每分钟180-220字
                chars_per_minute = 200
            else:
                # 英文语速：每分钟150-180词，约750-900字符
                chars_per_minute = 800
            
            # 根据模型和语音调整
            speed_factor = self._get_speed_factor(model, voice)
            chars_per_minute *= speed_factor
            
            # 计算总时长（毫秒）
            total_duration_ms = (total_chars / chars_per_minute * 60 * 1000)
            
            # 添加句子间的自然停顿时间
            pause_time = len(sentence_weights) * 100  # 每个句子间100ms停顿
            total_duration_ms += pause_time
        
        # 生成字幕时间戳
        subtitles = []
        current_time = 0
        
        # 预留一点开始时间（避免第一个字幕太早出现）
        start_buffer = 100  # 100ms缓冲
        current_time = start_buffer
        
        # 计算可用的字幕时间（去掉开始和结束缓冲）
        end_buffer = 200  # 200ms结束缓冲
        available_duration = total_duration_ms - start_buffer - end_buffer
        
        for i, (sentence, char_count) in enumerate(sentence_weights):
            # 根据字符数比例分配时间
            weight = char_count / total_chars if total_chars > 0 else 1 / len(sentence_weights)
            duration = available_duration * weight
            
            # 确保最小显示时间
            min_duration = 500  # 至少显示500ms
            duration = max(duration, min_duration)
            
            # 确保不超过结束时间
            if current_time + duration > total_duration_ms - end_buffer:
                duration = total_duration_ms - end_buffer - current_time
            
            subtitle = {
                "index": i + 1,
                "content": sentence,
                "start": int(current_time),
                "end": int(current_time + duration),
                "start_time": current_time,
                "end_time": current_time + duration
            }
            
            subtitles.append(subtitle)
            
            # 更新时间，添加小的间隔
            gap = 50 if i < len(sentence_weights) - 1 else 0  # 句子间50ms间隔
            current_time += duration + gap
        
        print(f"✅ 生成了 {len(subtitles)} 条字幕")
        print(f"实际/估算总时长: {total_duration_ms/1000:.2f} 秒")
        
        # 更新结果
        result["subtitles"] = subtitles
        result["actual_duration_ms"] = total_duration_ms
        result["actual_duration_s"] = total_duration_ms / 1000
        
        return result
    
    def _get_speed_factor(self, model: str, voice: str) -> float:
        """获取基于模型和语音的速度因子"""
        # 模型速度因子
        model_factors = {
            "tts-1": 1.0,
            "tts-1-hd": 0.95  # HD模型稍慢
        }
        
        # 语音速度因子
        voice_factors = {
            "alloy": 1.0,
            "echo": 0.95,     # 较慢
            "fable": 1.05,    # 较快
            "onyx": 0.9,      # 最慢
            "nova": 1.1,      # 最快
            "shimmer": 1.0
        }
        
        model_factor = model_factors.get(model, 1.0)
        voice_factor = voice_factors.get(voice, 1.0)
        
        return model_factor * voice_factor
    
    def _split_text_to_sentences(self, text: str) -> List[str]:
        """改进的文本分割方法"""
        import re
        
        # 保留原始的标点符号
        # 中英文句子结束符
        sentence_endings = r'([。！？.!?]+)'
        
        # 使用正则表达式分割，但保留分隔符
        parts = re.split(sentence_endings, text)
        
        sentences = []
        current_sentence = ""
        
        for i, part in enumerate(parts):
            if re.match(sentence_endings, part):
                # 这是标点符号，添加到当前句子
                current_sentence += part
                sentences.append(current_sentence.strip())
                current_sentence = ""
            else:
                # 这是文本内容
                current_sentence += part
        
        # 添加最后一个句子（如果有）
        if current_sentence.strip():
            sentences.append(current_sentence.strip())
        
        # 如果没有句子或句子太少，按其他标点分割
        if len(sentences) <= 1:
            text_to_split = text if not sentences else sentences[0]
            # 次要分隔符
            secondary_delimiters = r'([，,；;：:])'
            parts = re.split(secondary_delimiters, text_to_split)
            
            sentences = []
            current_sentence = ""
            
            for i, part in enumerate(parts):
                if re.match(secondary_delimiters, part):
                    current_sentence += part
                    if len(current_sentence) > 20:  # 如果句子够长，就分割
                        sentences.append(current_sentence.strip())
                        current_sentence = ""
                else:
                    current_sentence += part
            
            if current_sentence.strip():
                sentences.append(current_sentence.strip())
        
        # 处理过长的句子
        final_sentences = []
        max_length = 50  # 每个字幕最多50个字符
        
        for sentence in sentences:
            if len(sentence) <= max_length:
                final_sentences.append(sentence)
            else:
                # 智能分割长句子
                if any('\u4e00' <= char <= '\u9fff' for char in sentence):
                    # 中文按字符分割
                    for i in range(0, len(sentence), max_length):
                        final_sentences.append(sentence[i:i+max_length].strip())
                else:
                    # 英文按单词边界分割
                    words = sentence.split()
                    current_chunk = []
                    current_length = 0
                    
                    for word in words:
                        if current_length + len(word) + len(current_chunk) > max_length and current_chunk:
                            final_sentences.append(' '.join(current_chunk))
                            current_chunk = [word]
                            current_length = len(word)
                        else:
                            current_chunk.append(word)
                            current_length += len(word) + 1
                    
                    if current_chunk:
                        final_sentences.append(' '.join(current_chunk))
        
        # 过滤空句子
        final_sentences = [s for s in final_sentences if s.strip()]
        
        return final_sentences


# 保留原有的辅助函数和常量
def format_time_for_srt(milliseconds: int) -> str:
    """将毫秒转换为SRT时间格式"""
    total_seconds = milliseconds / 1000
    hours = int(total_seconds // 3600)
    minutes = int((total_seconds % 3600) // 60)
    seconds = int(total_seconds % 60)
    ms = int((total_seconds % 1) * 1000)
    
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{ms:03d}"


def convert_gpt_subtitles_to_srt(subtitles: list) -> str:
    """将GPT TTS生成的字幕转换为SRT格式"""
    srt_content = []
    
    for subtitle in subtitles:
        # 序号
        srt_content.append(str(subtitle['index']))
        
        # 时间轴
        start_time = format_time_for_srt(subtitle['start'])
        end_time = format_time_for_srt(subtitle['end'])
        srt_content.append(f"{start_time} --> {end_time}")
        
        # 文本内容
        srt_content.append(subtitle['content'])
        srt_content.append("")
    
    return '\n'.join(srt_content)


# GPT TTS 音色选项
GPT_VOICE_OPTIONS = {
    "标准音色": {
        "alloy": "Alloy - 中性、平衡的声音",
        "echo": "Echo - 温暖、友好的男声",
        "fable": "Fable - 英式英语、表现力强",
        "onyx": "Onyx - 深沉、共鸣的男声",
        "nova": "Nova - 年轻、充满活力的女声",
        "shimmer": "Shimmer - 柔和、愉快的女声"
    }
}

# 模型选项
GPT_MODEL_OPTIONS = {
    "tts-1": "标准质量 - 更快的生成速度",
    "tts-1-hd": "高质量 - 更好的音质"
}

# 格式选项
GPT_FORMAT_OPTIONS = {
    "mp3": "MP3 - 通用格式，文件较小",
    "opus": "Opus - 高质量、低延迟",
    "aac": "AAC - Apple设备优化",
    "flac": "FLAC - 无损音质"
}
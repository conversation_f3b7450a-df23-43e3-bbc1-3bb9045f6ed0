"""
YouTube视频管理系统GUI
使用PySide6实现的图形界面
"""

import sys
import os
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QPushButton, QTableWidget, QTableWidgetItem,
    QHeaderView, QLabel, QLineEdit, QTextEdit, QComboBox,
    QSpinBox, QCheckBox, QGroupBox, QFileDialog, QMessageBox,
    QDialog, QDialogButtonBox, QFormLayout, QListWidget,
    QSplitter, QProgressBar, QMenu, QToolBar, QStatusBar
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QSettings
from PySide6.QtGui import QAction, QIcon, QFont

from database import Database
from youtube_database import YouTubeDatabase
from youtube_auth import <PERSON><PERSON>uthManager
from youtube_uploader import YouTubeUploader
from video_manager import <PERSON>Manager
from upload_queue_manager import UploadQueueManager
from file_manager import FileManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class YouTubeAccountDialog(QDialog):
    """YouTube账号添加/编辑对话框"""
    
    def __init__(self, parent=None, account_data=None):
        super().__init__(parent)
        self.account_data = account_data
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("添加YouTube账号" if not self.account_data else "编辑YouTube账号")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 表单
        form_layout = QFormLayout()
        
        # 账号名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("用于识别的账号名称")
        if self.account_data:
            self.name_edit.setText(self.account_data.get('account_name', ''))
        form_layout.addRow("账号名称:", self.name_edit)
        
        # 邮箱
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("YouTube账号邮箱（可选）")
        if self.account_data:
            self.email_edit.setText(self.account_data.get('email', ''))
        form_layout.addRow("邮箱:", self.email_edit)
        
        # 客户端密钥文件
        client_layout = QHBoxLayout()
        self.client_file_edit = QLineEdit()
        self.client_file_edit.setPlaceholderText("OAuth2客户端密钥文件路径")
        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_client_file)
        client_layout.addWidget(self.client_file_edit)
        client_layout.addWidget(self.browse_btn)
        form_layout.addRow("客户端密钥:", client_layout)
        
        # 配额限制
        self.quota_spin = QSpinBox()
        self.quota_spin.setRange(1000, 100000)
        self.quota_spin.setValue(10000)
        self.quota_spin.setSuffix(" 单位")
        if self.account_data:
            self.quota_spin.setValue(self.account_data.get('quota_limit', 10000))
        form_layout.addRow("每日配额限制:", self.quota_spin)
        
        layout.addLayout(form_layout)
        
        # 说明
        info_label = QLabel(
            "说明：\n"
            "1. 需要先在Google Cloud Console创建OAuth2客户端\n"
            "2. 下载客户端密钥JSON文件\n"
            "3. 首次添加账号时会打开浏览器进行授权\n"
            "4. 授权成功后凭证会自动保存"
        )
        info_label.setWordWrap(True)
        info_label.setStyleSheet("QLabel { color: #666; padding: 10px; }")
        layout.addWidget(info_label)
        
        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
        
    def browse_client_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择客户端密钥文件", "", "JSON文件 (*.json)"
        )
        if file_path:
            self.client_file_edit.setText(file_path)
    
    def get_account_info(self):
        return {
            'account_name': self.name_edit.text(),
            'email': self.email_edit.text(),
            'client_secrets_file': self.client_file_edit.text(),
            'quota_limit': self.quota_spin.value()
        }


class VideoMetadataDialog(QDialog):
    """视频元数据编辑对话框"""
    
    def __init__(self, parent=None, video_data=None, templates=None):
        super().__init__(parent)
        self.video_data = video_data or {}
        self.templates = templates or []
        self.setup_ui()
        
    def setup_ui(self):
        self.setWindowTitle("编辑视频信息")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # 模板选择
        template_layout = QHBoxLayout()
        template_layout.addWidget(QLabel("应用模板:"))
        self.template_combo = QComboBox()
        self.template_combo.addItem("不使用模板")
        for template in self.templates:
            self.template_combo.addItem(template['template_name'], template)
        self.template_combo.currentIndexChanged.connect(self.apply_template)
        template_layout.addWidget(self.template_combo)
        template_layout.addStretch()
        layout.addLayout(template_layout)
        
        # 表单
        form_layout = QFormLayout()
        
        # 标题
        self.title_edit = QLineEdit()
        self.title_edit.setText(self.video_data.get('title', ''))
        form_layout.addRow("标题:", self.title_edit)
        
        # 描述
        self.description_edit = QTextEdit()
        self.description_edit.setText(self.video_data.get('description', ''))
        self.description_edit.setMaximumHeight(150)
        form_layout.addRow("描述:", self.description_edit)
        
        # 标签
        self.tags_edit = QLineEdit()
        tags = self.video_data.get('tags', [])
        if isinstance(tags, list):
            self.tags_edit.setText(', '.join(tags))
        else:
            self.tags_edit.setText(tags)
        self.tags_edit.setPlaceholderText("标签1, 标签2, 标签3")
        form_layout.addRow("标签:", self.tags_edit)
        
        # 分类
        self.category_combo = QComboBox()
        # 添加YouTube分类
        categories = {
            '1': '电影和动画',
            '2': '汽车',
            '10': '音乐',
            '15': '宠物和动物',
            '17': '体育',
            '19': '旅游和活动',
            '20': '游戏',
            '22': '人物和博客',
            '23': '喜剧',
            '24': '娱乐',
            '25': '新闻和政治',
            '26': '时尚和生活',
            '27': '教育',
            '28': '科技',
        }
        for cat_id, cat_name in categories.items():
            self.category_combo.addItem(cat_name, cat_id)
        
        current_category = self.video_data.get('category_id', '22')
        index = self.category_combo.findData(current_category)
        if index >= 0:
            self.category_combo.setCurrentIndex(index)
        
        form_layout.addRow("分类:", self.category_combo)
        
        # 隐私设置
        self.privacy_combo = QComboBox()
        self.privacy_combo.addItems(['private', 'unlisted', 'public'])
        self.privacy_combo.setCurrentText(self.video_data.get('privacy_status', 'private'))
        form_layout.addRow("隐私设置:", self.privacy_combo)
        
        layout.addLayout(form_layout)
        
        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def apply_template(self, index):
        if index <= 0:  # 不使用模板
            return
        
        template = self.template_combo.itemData(index)
        if template:
            if template.get('title_template'):
                self.title_edit.setText(template['title_template'])
            if template.get('description_template'):
                self.description_edit.setText(template['description_template'])
            if template.get('tags_template'):
                self.tags_edit.setText(', '.join(template['tags_template']))
            if template.get('category_id'):
                index = self.category_combo.findData(template['category_id'])
                if index >= 0:
                    self.category_combo.setCurrentIndex(index)
            if template.get('privacy_status'):
                self.privacy_combo.setCurrentText(template['privacy_status'])
    
    def get_metadata(self):
        tags_text = self.tags_edit.text()
        tags = [t.strip() for t in tags_text.split(',') if t.strip()]
        
        return {
            'title': self.title_edit.text(),
            'description': self.description_edit.toPlainText(),
            'tags': tags,
            'category_id': self.category_combo.currentData(),
            'privacy_status': self.privacy_combo.currentText()
        }


class YouTubeManagerWindow(QMainWindow):
    """YouTube视频管理器主窗口"""
    
    def __init__(self):
        super().__init__()
        
        # 初始化数据库和管理器
        self.project_db = Database()
        self.youtube_db = YouTubeDatabase()
        self.file_manager = FileManager()
        self.auth_manager = YouTubeAuthManager()
        self.video_manager = VideoManager(self.project_db, self.youtube_db, self.file_manager)
        self.queue_manager = UploadQueueManager(self.youtube_db, self.auth_manager)
        
        # 设置
        self.settings = QSettings('DigitalPeople', 'YouTubeManager')
        
        # 初始化UI
        self.setup_ui()
        
        # 加载数据
        self.refresh_all_data()
        
        # 启动定时器
        self.setup_timers()
        
    def setup_ui(self):
        self.setWindowTitle("YouTube视频管理系统")
        self.resize(1200, 800)
        
        # 中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        
        # 工具栏
        self.create_toolbar()
        
        # 标签页
        self.tab_widget = QTabWidget()
        
        # 创建各个页面
        self.create_videos_tab()
        self.create_accounts_tab()
        self.create_upload_tab()
        self.create_tasks_tab()
        self.create_logs_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.update_status_bar()
        
    def create_toolbar(self):
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # 扫描视频
        scan_action = QAction("扫描视频", self)
        scan_action.triggered.connect(self.scan_videos)
        toolbar.addAction(scan_action)
        
        # 添加账号
        add_account_action = QAction("添加账号", self)
        add_account_action.triggered.connect(self.add_account)
        toolbar.addAction(add_account_action)
        
        toolbar.addSeparator()
        
        # 队列控制
        self.start_queue_action = QAction("启动队列", self)
        self.start_queue_action.triggered.connect(self.start_queue)
        toolbar.addAction(self.start_queue_action)
        
        self.stop_queue_action = QAction("停止队列", self)
        self.stop_queue_action.triggered.connect(self.stop_queue)
        self.stop_queue_action.setEnabled(False)
        toolbar.addAction(self.stop_queue_action)
        
        toolbar.addSeparator()
        
        # 刷新
        refresh_action = QAction("刷新", self)
        refresh_action.triggered.connect(self.refresh_all_data)
        toolbar.addAction(refresh_action)
        
    def create_videos_tab(self):
        """创建视频管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        scan_btn = QPushButton("扫描新视频")
        scan_btn.clicked.connect(self.scan_videos)
        toolbar_layout.addWidget(scan_btn)
        
        import_btn = QPushButton("导入选中")
        import_btn.clicked.connect(self.import_selected_videos)
        toolbar_layout.addWidget(import_btn)
        
        edit_btn = QPushButton("编辑信息")
        edit_btn.clicked.connect(self.edit_video_metadata)
        toolbar_layout.addWidget(edit_btn)
        
        toolbar_layout.addStretch()
        
        # 过滤器
        toolbar_layout.addWidget(QLabel("状态:"))
        self.video_status_filter = QComboBox()
        self.video_status_filter.addItems(['全部', 'generated', 'ready', 'uploaded', 'failed'])
        self.video_status_filter.currentTextChanged.connect(self.filter_videos)
        toolbar_layout.addWidget(self.video_status_filter)
        
        layout.addLayout(toolbar_layout)
        
        # 视频表格
        self.videos_table = QTableWidget()
        self.videos_table.setColumnCount(9)
        self.videos_table.setHorizontalHeaderLabels([
            '选择', 'ID', '文件名', '项目', '标题', '时长', '大小', '状态', '创建时间'
        ])
        self.videos_table.horizontalHeader().setStretchLastSection(True)
        self.videos_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.videos_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.videos_table.customContextMenuRequested.connect(self.show_video_context_menu)
        
        layout.addWidget(self.videos_table)
        
        self.tab_widget.addTab(widget, "视频管理")
        
    def create_accounts_tab(self):
        """创建账号管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        add_btn = QPushButton("添加账号")
        add_btn.clicked.connect(self.add_account)
        toolbar_layout.addWidget(add_btn)
        
        auth_btn = QPushButton("重新授权")
        auth_btn.clicked.connect(self.reauth_account)
        toolbar_layout.addWidget(auth_btn)
        
        remove_btn = QPushButton("删除账号")
        remove_btn.clicked.connect(self.remove_account)
        toolbar_layout.addWidget(remove_btn)
        
        toolbar_layout.addStretch()
        layout.addLayout(toolbar_layout)
        
        # 账号表格
        self.accounts_table = QTableWidget()
        self.accounts_table.setColumnCount(8)
        self.accounts_table.setHorizontalHeaderLabels([
            'ID', '账号名称', '邮箱', '频道名', '订阅数', '配额使用', '状态', '创建时间'
        ])
        self.accounts_table.horizontalHeader().setStretchLastSection(True)
        self.accounts_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.accounts_table)
        
        self.tab_widget.addTab(widget, "账号管理")
        
    def create_upload_tab(self):
        """创建批量上传标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 上传配置
        config_group = QGroupBox("上传配置")
        config_layout = QFormLayout()
        
        # 账号选择
        self.upload_accounts_list = QListWidget()
        self.upload_accounts_list.setSelectionMode(QListWidget.MultiSelection)
        self.upload_accounts_list.setMaximumHeight(100)
        config_layout.addRow("选择账号:", self.upload_accounts_list)
        
        # 调度设置
        schedule_layout = QHBoxLayout()
        
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(5, 120)
        self.interval_spin.setValue(10)
        self.interval_spin.setSuffix(" 分钟")
        schedule_layout.addWidget(QLabel("发布间隔:"))
        schedule_layout.addWidget(self.interval_spin)
        
        self.daily_limit_spin = QSpinBox()
        self.daily_limit_spin.setRange(1, 50)
        self.daily_limit_spin.setValue(5)
        self.daily_limit_spin.setSuffix(" 个/天")
        schedule_layout.addWidget(QLabel("每日限制:"))
        schedule_layout.addWidget(self.daily_limit_spin)
        
        schedule_layout.addStretch()
        config_layout.addRow("调度设置:", schedule_layout)
        
        # 元数据模板
        self.template_combo = QComboBox()
        self.refresh_templates()
        config_layout.addRow("元数据模板:", self.template_combo)
        
        config_group.setLayout(config_layout)
        layout.addWidget(config_group)
        
        # 待上传视频列表
        videos_group = QGroupBox("待上传视频")
        videos_layout = QVBoxLayout()
        
        # 工具栏
        toolbar = QHBoxLayout()
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all_upload_videos)
        toolbar.addWidget(select_all_btn)
        
        select_none_btn = QPushButton("取消全选")
        select_none_btn.clicked.connect(self.select_none_upload_videos)
        toolbar.addWidget(select_none_btn)
        
        toolbar.addStretch()
        videos_layout.addLayout(toolbar)
        
        # 视频列表
        self.upload_videos_table = QTableWidget()
        self.upload_videos_table.setColumnCount(6)
        self.upload_videos_table.setHorizontalHeaderLabels([
            '选择', 'ID', '文件名', '标题', '时长', '大小'
        ])
        self.upload_videos_table.horizontalHeader().setStretchLastSection(True)
        videos_layout.addWidget(self.upload_videos_table)
        
        videos_group.setLayout(videos_layout)
        layout.addWidget(videos_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        create_tasks_btn = QPushButton("创建上传任务")
        create_tasks_btn.clicked.connect(self.create_upload_tasks)
        button_layout.addWidget(create_tasks_btn)
        
        layout.addLayout(button_layout)
        
        self.tab_widget.addTab(widget, "批量上传")
        
    def create_tasks_tab(self):
        """创建任务监控标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 队列状态
        status_group = QGroupBox("队列状态")
        status_layout = QFormLayout()
        
        self.queue_status_label = QLabel("未运行")
        status_layout.addRow("状态:", self.queue_status_label)
        
        self.queue_stats_label = QLabel("--")
        status_layout.addRow("统计:", self.queue_stats_label)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        retry_btn = QPushButton("重试失败任务")
        retry_btn.clicked.connect(self.retry_failed_tasks)
        toolbar_layout.addWidget(retry_btn)
        
        pause_btn = QPushButton("暂停选中")
        pause_btn.clicked.connect(self.pause_selected_tasks)
        toolbar_layout.addWidget(pause_btn)
        
        cancel_btn = QPushButton("取消选中")
        cancel_btn.clicked.connect(self.cancel_selected_tasks)
        toolbar_layout.addWidget(cancel_btn)
        
        toolbar_layout.addStretch()
        
        # 过滤器
        toolbar_layout.addWidget(QLabel("状态:"))
        self.task_status_filter = QComboBox()
        self.task_status_filter.addItems(['全部', 'pending', 'processing', 'completed', 'failed', 'cancelled'])
        self.task_status_filter.currentTextChanged.connect(self.filter_tasks)
        toolbar_layout.addWidget(self.task_status_filter)
        
        layout.addLayout(toolbar_layout)
        
        # 任务表格
        self.tasks_table = QTableWidget()
        self.tasks_table.setColumnCount(10)
        self.tasks_table.setHorizontalHeaderLabels([
            'ID', '视频', '账号', '状态', '优先级', '重试次数', 
            'YouTube ID', '错误信息', '创建时间', '完成时间'
        ])
        self.tasks_table.horizontalHeader().setStretchLastSection(True)
        self.tasks_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.tasks_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tasks_table.customContextMenuRequested.connect(self.show_task_context_menu)
        
        layout.addWidget(self.tasks_table)
        
        self.tab_widget.addTab(widget, "任务监控")
        
    def create_logs_tab(self):
        """创建日志查看标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 过滤器
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("任务ID:"))
        self.log_task_filter = QLineEdit()
        self.log_task_filter.setMaximumWidth(100)
        self.log_task_filter.textChanged.connect(self.filter_logs)
        filter_layout.addWidget(self.log_task_filter)
        
        filter_layout.addWidget(QLabel("视频ID:"))
        self.log_video_filter = QLineEdit()
        self.log_video_filter.setMaximumWidth(100)
        self.log_video_filter.textChanged.connect(self.filter_logs)
        filter_layout.addWidget(self.log_video_filter)
        
        clear_btn = QPushButton("清除过滤")
        clear_btn.clicked.connect(self.clear_log_filters)
        filter_layout.addWidget(clear_btn)
        
        filter_layout.addStretch()
        layout.addLayout(filter_layout)
        
        # 日志表格
        self.logs_table = QTableWidget()
        self.logs_table.setColumnCount(7)
        self.logs_table.setHorizontalHeaderLabels([
            'ID', '任务ID', '视频ID', '账号ID', '操作', '消息', '时间'
        ])
        self.logs_table.horizontalHeader().setStretchLastSection(True)
        self.logs_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.logs_table)
        
        self.tab_widget.addTab(widget, "上传日志")
        
    def setup_timers(self):
        """设置定时器"""
        # 队列状态更新定时器
        self.queue_timer = QTimer()
        self.queue_timer.timeout.connect(self.update_queue_status)
        self.queue_timer.start(5000)  # 每5秒更新
        
        # 数据刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_current_tab)
        self.refresh_timer.start(30000)  # 每30秒刷新当前标签页
        
    # ========== 槽函数 ==========
    
    def scan_videos(self):
        """扫描视频"""
        reply = QMessageBox.question(
            self, "扫描视频", 
            "是否扫描所有项目目录和根目录中的视频文件？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            result = self.video_manager.auto_import_all_videos()
            
            QMessageBox.information(
                self, "扫描完成",
                f"扫描完成！\n"
                f"发现视频: {result['total_found']}\n"
                f"成功导入: {result['imported']}\n"
                f"失败: {result['failed']}"
            )
            
            self.refresh_videos()
    
    def add_account(self):
        """添加账号"""
        dialog = YouTubeAccountDialog(self)
        if dialog.exec():
            info = dialog.get_account_info()
            
            if not info['account_name'] or not info['client_secrets_file']:
                QMessageBox.warning(self, "错误", "账号名称和客户端密钥文件不能为空")
                return
            
            try:
                # 认证账号
                auth_info = self.auth_manager.authenticate_account(
                    info['account_name'],
                    info['client_secrets_file']
                )
                
                # 保存到数据库
                account_id = self.youtube_db.add_youtube_account(
                    account_name=auth_info['account_name'],
                    email=info['email'],
                    credentials_path=auth_info['credentials_path'],
                    channel_id=auth_info['channel_id'],
                    channel_name=auth_info['channel_name'],
                    quota_limit=info['quota_limit']
                )
                
                QMessageBox.information(
                    self, "成功",
                    f"账号添加成功！\n"
                    f"频道名: {auth_info['channel_name']}\n"
                    f"订阅数: {auth_info['subscriber_count']}"
                )
                
                self.refresh_accounts()
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"添加账号失败：\n{str(e)}")
    
    def start_queue(self):
        """启动队列"""
        self.queue_manager.start()
        self.start_queue_action.setEnabled(False)
        self.stop_queue_action.setEnabled(True)
        self.update_status_bar()
    
    def stop_queue(self):
        """停止队列"""
        self.queue_manager.stop()
        self.start_queue_action.setEnabled(True)
        self.stop_queue_action.setEnabled(False)
        self.update_status_bar()
    
    def create_upload_tasks(self):
        """创建上传任务"""
        # 获取选中的账号
        selected_accounts = []
        for i in range(self.upload_accounts_list.count()):
            item = self.upload_accounts_list.item(i)
            if item.isSelected():
                account_id = item.data(Qt.UserRole)
                selected_accounts.append(account_id)
        
        if not selected_accounts:
            QMessageBox.warning(self, "警告", "请至少选择一个账号")
            return
        
        # 获取选中的视频
        selected_videos = []
        for row in range(self.upload_videos_table.rowCount()):
            checkbox = self.upload_videos_table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                video_id = int(self.upload_videos_table.item(row, 1).text())
                selected_videos.append(video_id)
        
        if not selected_videos:
            QMessageBox.warning(self, "警告", "请至少选择一个视频")
            return
        
        # 获取配置
        schedule_config = {
            'interval_minutes': self.interval_spin.value(),
            'daily_limit': self.daily_limit_spin.value()
        }
        
        # 应用模板（如果选择了）
        template_id = self.template_combo.currentData()
        if template_id:
            for video_id in selected_videos:
                self.video_manager.prepare_video_for_upload(video_id, template_id)
        
        # 创建任务
        task_ids = self.queue_manager.add_batch_upload_tasks(
            selected_videos, selected_accounts, schedule_config
        )
        
        QMessageBox.information(
            self, "成功",
            f"成功创建 {len(task_ids)} 个上传任务！"
        )
        
        self.refresh_tasks()
        self.refresh_videos()
    
    # ========== 数据刷新 ==========
    
    def refresh_all_data(self):
        """刷新所有数据"""
        self.refresh_videos()
        self.refresh_accounts()
        self.refresh_tasks()
        self.refresh_logs()
        self.refresh_upload_tab()
        self.update_status_bar()
    
    def refresh_current_tab(self):
        """刷新当前标签页"""
        current_index = self.tab_widget.currentIndex()
        if current_index == 0:
            self.refresh_videos()
        elif current_index == 1:
            self.refresh_accounts()
        elif current_index == 2:
            self.refresh_upload_tab()
        elif current_index == 3:
            self.refresh_tasks()
        elif current_index == 4:
            self.refresh_logs()
    
    def refresh_videos(self):
        """刷新视频列表"""
        self.videos_table.setRowCount(0)
        
        # 获取过滤状态
        status_filter = self.video_status_filter.currentText()
        
        # 获取所有视频
        all_videos = []
        if status_filter == '全部':
            for status in ['generated', 'ready', 'uploading', 'uploaded', 'failed']:
                videos = self.youtube_db.get_videos_by_status(status)
                all_videos.extend(videos)
        else:
            all_videos = self.youtube_db.get_videos_by_status(status_filter)
        
        # 填充表格
        for video in all_videos:
            row = self.videos_table.rowCount()
            self.videos_table.insertRow(row)
            
            # 复选框
            checkbox = QCheckBox()
            self.videos_table.setCellWidget(row, 0, checkbox)
            
            # 数据
            self.videos_table.setItem(row, 1, QTableWidgetItem(str(video['id'])))
            self.videos_table.setItem(row, 2, QTableWidgetItem(os.path.basename(video['file_path'])))
            self.videos_table.setItem(row, 3, QTableWidgetItem(str(video.get('project_id', ''))))
            self.videos_table.setItem(row, 4, QTableWidgetItem(video.get('title', '')))
            
            # 时长
            duration = video.get('duration', 0)
            duration_str = f"{duration//60}:{duration%60:02d}" if duration else ""
            self.videos_table.setItem(row, 5, QTableWidgetItem(duration_str))
            
            # 大小
            size = video.get('file_size', 0)
            size_str = f"{size/(1024*1024):.1f} MB" if size else ""
            self.videos_table.setItem(row, 6, QTableWidgetItem(size_str))
            
            self.videos_table.setItem(row, 7, QTableWidgetItem(video['status']))
            self.videos_table.setItem(row, 8, QTableWidgetItem(str(video['created_at'])))
    
    def refresh_accounts(self):
        """刷新账号列表"""
        self.accounts_table.setRowCount(0)
        
        accounts = self.youtube_db.get_active_youtube_accounts()
        
        for account in accounts:
            row = self.accounts_table.rowCount()
            self.accounts_table.insertRow(row)
            
            self.accounts_table.setItem(row, 0, QTableWidgetItem(str(account['id'])))
            self.accounts_table.setItem(row, 1, QTableWidgetItem(account['account_name']))
            self.accounts_table.setItem(row, 2, QTableWidgetItem(account.get('email', '')))
            self.accounts_table.setItem(row, 3, QTableWidgetItem(account.get('channel_name', '')))
            self.accounts_table.setItem(row, 4, QTableWidgetItem(str(account.get('subscriber_count', '0'))))
            
            # 配额使用
            quota_used = account.get('quota_used', 0)
            quota_limit = account.get('quota_limit', 10000)
            quota_str = f"{quota_used}/{quota_limit} ({quota_used/quota_limit*100:.1f}%)"
            self.accounts_table.setItem(row, 5, QTableWidgetItem(quota_str))
            
            status = "活跃" if account.get('is_active') else "停用"
            self.accounts_table.setItem(row, 6, QTableWidgetItem(status))
            self.accounts_table.setItem(row, 7, QTableWidgetItem(str(account['created_at'])))
    
    def refresh_tasks(self):
        """刷新任务列表"""
        # 实现任务列表刷新逻辑
        pass
    
    def refresh_logs(self):
        """刷新日志列表"""
        # 实现日志列表刷新逻辑
        pass
    
    def refresh_upload_tab(self):
        """刷新上传标签页"""
        # 刷新账号列表
        self.upload_accounts_list.clear()
        accounts = self.youtube_db.get_active_youtube_accounts()
        for account in accounts:
            item = self.upload_accounts_list.addItem(account['account_name'])
            self.upload_accounts_list.item(self.upload_accounts_list.count()-1).setData(
                Qt.UserRole, account['id']
            )
        
        # 刷新待上传视频
        self.upload_videos_table.setRowCount(0)
        videos = self.youtube_db.get_videos_by_status('ready')
        
        for video in videos:
            row = self.upload_videos_table.rowCount()
            self.upload_videos_table.insertRow(row)
            
            # 复选框
            checkbox = QCheckBox()
            self.upload_videos_table.setCellWidget(row, 0, checkbox)
            
            # 数据
            self.upload_videos_table.setItem(row, 1, QTableWidgetItem(str(video['id'])))
            self.upload_videos_table.setItem(row, 2, QTableWidgetItem(os.path.basename(video['file_path'])))
            self.upload_videos_table.setItem(row, 3, QTableWidgetItem(video.get('title', '')))
            
            # 时长
            duration = video.get('duration', 0)
            duration_str = f"{duration//60}:{duration%60:02d}" if duration else ""
            self.upload_videos_table.setItem(row, 4, QTableWidgetItem(duration_str))
            
            # 大小
            size = video.get('file_size', 0)
            size_str = f"{size/(1024*1024):.1f} MB" if size else ""
            self.upload_videos_table.setItem(row, 5, QTableWidgetItem(size_str))
    
    def refresh_templates(self):
        """刷新模板列表"""
        self.template_combo.clear()
        self.template_combo.addItem("不使用模板", None)
        
        templates = self.youtube_db.get_metadata_templates()
        for template in templates:
            self.template_combo.addItem(template['template_name'], template['id'])
    
    def update_status_bar(self):
        """更新状态栏"""
        stats = self.youtube_db.get_upload_statistics()
        
        status_text = f"视频总数: {stats['total_videos']} | "
        status_text += f"已上传: {stats['video_status_counts'].get('uploaded', 0)} | "
        status_text += f"任务总数: {stats['total_tasks']} | "
        status_text += f"今日上传: {stats['today_uploads']}"
        
        if self.queue_manager._running:
            status_text += " | 队列: 运行中"
        else:
            status_text += " | 队列: 已停止"
        
        self.status_bar.showMessage(status_text)
    
    def update_queue_status(self):
        """更新队列状态"""
        if hasattr(self, 'queue_status_label'):
            status = self.queue_manager.get_queue_status()
            
            if status['running']:
                self.queue_status_label.setText(f"运行中 (运行时间: {status['uptime']})")
                self.queue_status_label.setStyleSheet("color: green;")
            else:
                self.queue_status_label.setText("未运行")
                self.queue_status_label.setStyleSheet("color: red;")
            
            stats_text = f"活跃任务: {status['active_tasks']}/{status['max_workers']} | "
            stats_text += f"队列大小: {status['queue_size']} | "
            stats_text += f"已处理: {status['processed']} | "
            stats_text += f"成功率: {status['success_rate']}"
            
            self.queue_stats_label.setText(stats_text)
    
    # ========== 辅助函数 ==========
    
    def filter_videos(self):
        """过滤视频"""
        self.refresh_videos()
    
    def filter_tasks(self):
        """过滤任务"""
        self.refresh_tasks()
    
    def filter_logs(self):
        """过滤日志"""
        self.refresh_logs()
    
    def clear_log_filters(self):
        """清除日志过滤"""
        self.log_task_filter.clear()
        self.log_video_filter.clear()
        self.refresh_logs()
    
    def select_all_upload_videos(self):
        """全选上传视频"""
        for row in range(self.upload_videos_table.rowCount()):
            checkbox = self.upload_videos_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)
    
    def select_none_upload_videos(self):
        """取消全选上传视频"""
        for row in range(self.upload_videos_table.rowCount()):
            checkbox = self.upload_videos_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)
    
    def import_selected_videos(self):
        """导入选中的视频"""
        # 实现视频导入逻辑
        pass
    
    def edit_video_metadata(self):
        """编辑视频元数据"""
        # 实现视频元数据编辑逻辑
        pass
    
    def reauth_account(self):
        """重新授权账号"""
        # 实现账号重新授权逻辑
        pass
    
    def remove_account(self):
        """删除账号"""
        # 实现账号删除逻辑
        pass
    
    def retry_failed_tasks(self):
        """重试失败的任务"""
        count = self.queue_manager.retry_failed_tasks()
        QMessageBox.information(self, "完成", f"已重置 {count} 个失败任务")
        self.refresh_tasks()
    
    def pause_selected_tasks(self):
        """暂停选中的任务"""
        # 实现任务暂停逻辑
        pass
    
    def cancel_selected_tasks(self):
        """取消选中的任务"""
        # 实现任务取消逻辑
        pass
    
    def show_video_context_menu(self, pos):
        """显示视频右键菜单"""
        # 实现视频右键菜单
        pass
    
    def show_task_context_menu(self, pos):
        """显示任务右键菜单"""
        # 实现任务右键菜单
        pass


def main():
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("YouTube视频管理系统")
    app.setOrganizationName("DigitalPeople")
    
    # 创建主窗口
    window = YouTubeManagerWindow()
    window.show()
    
    sys.exit(app.exec())


if __name__ == '__main__':
    main()
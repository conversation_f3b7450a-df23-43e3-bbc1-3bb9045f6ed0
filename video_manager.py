"""
视频管理模块
扫描、导入、管理生成的视频文件
"""

import os
import json
import logging
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import subprocess

from database import Database
from youtube_database import YouTubeDatabase
from file_manager import FileManager

logger = logging.getLogger(__name__)

class VideoManager:
    """视频管理器"""
    
    def __init__(self, project_db: Database, youtube_db: YouTubeDatabase, 
                 file_manager: FileManager):
        self.project_db = project_db
        self.youtube_db = youtube_db
        self.file_manager = file_manager
    
    def scan_for_videos(self, scan_path: str = None) -> List[Dict[str, Any]]:
        """
        扫描视频文件
        
        Args:
            scan_path: 扫描路径，如果为None则扫描所有项目目录
            
        Returns:
            找到的视频文件列表
        """
        videos_found = []
        
        if scan_path:
            # 扫描指定路径
            videos_found.extend(self._scan_directory(scan_path))
        else:
            # 扫描所有项目目录
            projects = self.project_db.get_all_projects()
            for project in projects:
                project_dir = self.file_manager.get_project_directory(project['id'])
                if project_dir.exists():
                    videos_found.extend(self._scan_directory(str(project_dir), project['id']))
            
            # 也扫描根目录下的视频（如generate_video_from_images_v5_volcano.py生成的）
            root_videos = self._scan_directory(".", project_id=None)
            videos_found.extend(root_videos)
        
        return videos_found
    
    def _scan_directory(self, directory: str, project_id: int = None) -> List[Dict[str, Any]]:
        """扫描目录中的视频文件"""
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv', '.wmv'}
        videos = []
        
        try:
            for root, dirs, files in os.walk(directory):
                # 跳过隐藏目录
                dirs[:] = [d for d in dirs if not d.startswith('.')]
                
                for file in files:
                    if any(file.lower().endswith(ext) for ext in video_extensions):
                        file_path = os.path.join(root, file)
                        
                        # 检查是否已经在数据库中
                        if not self._is_video_in_database(file_path):
                            video_info = self._get_video_info(file_path)
                            video_info['project_id'] = project_id
                            videos.append(video_info)
                            logger.info(f"发现新视频: {file_path}")
        
        except Exception as e:
            logger.error(f"扫描目录失败 {directory}: {str(e)}")
        
        return videos
    
    def _is_video_in_database(self, file_path: str) -> bool:
        """检查视频是否已在数据库中"""
        # 使用文件路径的哈希作为唯一标识
        videos = self.youtube_db.get_videos_by_status('generated')
        videos.extend(self.youtube_db.get_videos_by_status('ready'))
        videos.extend(self.youtube_db.get_videos_by_status('uploaded'))
        
        for video in videos:
            if video['file_path'] == file_path:
                return True
        return False
    
    def _get_video_info(self, file_path: str) -> Dict[str, Any]:
        """获取视频文件信息"""
        info = {
            'file_path': file_path,
            'file_name': os.path.basename(file_path),
            'file_size': os.path.getsize(file_path),
            'created_at': datetime.fromtimestamp(os.path.getctime(file_path)),
            'modified_at': datetime.fromtimestamp(os.path.getmtime(file_path))
        }
        
        # 尝试获取视频元数据
        try:
            metadata = self._extract_video_metadata(file_path)
            info.update(metadata)
        except Exception as e:
            logger.warning(f"无法提取视频元数据 {file_path}: {str(e)}")
        
        # 尝试根据文件名推断信息
        self._infer_info_from_filename(info)
        
        return info
    
    def _extract_video_metadata(self, file_path: str) -> Dict[str, Any]:
        """使用ffprobe提取视频元数据"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', file_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                data = json.loads(result.stdout)
                
                # 提取视频流信息
                video_stream = next((s for s in data.get('streams', []) 
                                   if s.get('codec_type') == 'video'), None)
                
                metadata = {}
                
                if video_stream:
                    metadata['resolution'] = f"{video_stream.get('width')}x{video_stream.get('height')}"
                    metadata['codec'] = video_stream.get('codec_name')
                    metadata['fps'] = eval(video_stream.get('r_frame_rate', '0/1'))
                
                # 提取格式信息
                format_info = data.get('format', {})
                metadata['duration'] = int(float(format_info.get('duration', 0)))
                metadata['bitrate'] = int(format_info.get('bit_rate', 0))
                
                return metadata
        
        except Exception as e:
            logger.error(f"ffprobe失败: {str(e)}")
        
        return {}
    
    def _infer_info_from_filename(self, info: Dict[str, Any]):
        """从文件名推断信息"""
        filename = info['file_name']
        
        # 常见的文件名模式
        if 'output_video_v5' in filename:
            info['title'] = '火山引擎生成视频'
            info['description'] = '使用火山引擎API生成的视频'
        elif 'scene_' in filename:
            info['title'] = '场景视频'
        elif filename.startswith('video_'):
            # 尝试提取时间戳
            parts = filename.split('_')
            if len(parts) >= 2:
                timestamp = parts[1].split('.')[0]
                info['title'] = f'视频_{timestamp}'
    
    def import_video(self, video_info: Dict[str, Any], auto_move: bool = True) -> int:
        """
        导入视频到数据库
        
        Args:
            video_info: 视频信息字典
            auto_move: 是否自动移动到项目目录
            
        Returns:
            视频ID
        """
        file_path = video_info['file_path']
        project_id = video_info.get('project_id')
        
        # 如果有项目ID且需要移动文件
        if project_id and auto_move:
            # 检查是否已在项目目录中
            project_dir = self.file_manager.get_project_directory(project_id)
            if not file_path.startswith(str(project_dir)):
                # 移动到项目目录
                new_path = self.file_manager.save_video_file(project_id, file_path)
                video_info['file_path'] = new_path
                
                # 删除原文件
                try:
                    os.remove(file_path)
                except:
                    pass
        
        # 添加到数据库
        video_id = self.youtube_db.add_video(
            project_id=project_id,
            file_path=video_info['file_path'],
            title=video_info.get('title'),
            description=video_info.get('description'),
            tags=video_info.get('tags'),
            duration=video_info.get('duration'),
            resolution=video_info.get('resolution'),
            category_id=video_info.get('category_id', '22'),  # 默认People & Blogs
            privacy_status=video_info.get('privacy_status', 'private')
        )
        
        logger.info(f"导入视频成功: ID={video_id}, 路径={video_info['file_path']}")
        return video_id
    
    def auto_import_all_videos(self) -> Dict[str, Any]:
        """自动导入所有找到的视频"""
        # 扫描视频
        videos_found = self.scan_for_videos()
        
        imported = []
        failed = []
        
        for video_info in videos_found:
            try:
                video_id = self.import_video(video_info)
                imported.append({
                    'video_id': video_id,
                    'file_name': video_info['file_name'],
                    'project_id': video_info.get('project_id')
                })
            except Exception as e:
                logger.error(f"导入视频失败 {video_info['file_path']}: {str(e)}")
                failed.append({
                    'file_path': video_info['file_path'],
                    'error': str(e)
                })
        
        return {
            'total_found': len(videos_found),
            'imported': len(imported),
            'failed': len(failed),
            'imported_videos': imported,
            'failed_videos': failed
        }
    
    def prepare_video_for_upload(self, video_id: int, template_id: int = None) -> bool:
        """
        准备视频以供上传
        应用元数据模板，生成标题、描述等
        
        Args:
            video_id: 视频ID
            template_id: 元数据模板ID（可选）
            
        Returns:
            是否成功
        """
        try:
            video = self.youtube_db.get_video(video_id)
            if not video:
                raise ValueError(f"视频不存在: {video_id}")
            
            # 如果指定了模板，应用模板
            if template_id:
                templates = self.youtube_db.get_metadata_templates()
                template = next((t for t in templates if t['id'] == template_id), None)
                
                if template:
                    # 应用模板
                    updates = {}
                    
                    # 处理标题模板
                    if template.get('title_template'):
                        title = self._process_template(
                            template['title_template'],
                            video,
                            video.get('project_id')
                        )
                        updates['title'] = title
                    
                    # 处理描述模板
                    if template.get('description_template'):
                        description = self._process_template(
                            template['description_template'],
                            video,
                            video.get('project_id')
                        )
                        updates['description'] = description
                    
                    # 应用其他设置
                    if template.get('tags_template'):
                        updates['tags'] = template['tags_template']
                    if template.get('category_id'):
                        updates['category_id'] = template['category_id']
                    if template.get('privacy_status'):
                        updates['privacy_status'] = template['privacy_status']
                    
                    # 更新视频信息
                    # 这里需要扩展youtube_database来支持更新视频信息
                    # 暂时简化处理
                    logger.info(f"应用模板 {template_id} 到视频 {video_id}")
            
            # 更新视频状态为ready
            self.youtube_db.update_video_status(video_id, 'ready')
            return True
            
        except Exception as e:
            logger.error(f"准备视频失败: {str(e)}")
            return False
    
    def _process_template(self, template: str, video: Dict[str, Any], 
                         project_id: int = None) -> str:
        """处理模板变量"""
        # 获取项目信息
        project_name = "未命名项目"
        if project_id:
            project = self.project_db.get_project(project_id)
            if project:
                project_name = project.get('name', project_name)
        
        # 替换变量
        replacements = {
            '{project_name}': project_name,
            '{date}': datetime.now().strftime('%Y-%m-%d'),
            '{time}': datetime.now().strftime('%H:%M'),
            '{file_name}': os.path.basename(video.get('file_path', '')),
            '{duration}': str(video.get('duration', 0)),
            '{resolution}': video.get('resolution', '未知')
        }
        
        result = template
        for key, value in replacements.items():
            result = result.replace(key, value)
        
        return result
    
    def get_video_statistics(self) -> Dict[str, Any]:
        """获取视频统计信息"""
        stats = self.youtube_db.get_upload_statistics()
        
        # 添加额外的统计
        all_videos = []
        for status in ['generated', 'ready', 'uploading', 'uploaded', 'failed']:
            videos = self.youtube_db.get_videos_by_status(status)
            all_videos.extend(videos)
        
        # 计算总大小
        total_size = sum(v.get('file_size', 0) for v in all_videos)
        
        # 计算总时长
        total_duration = sum(v.get('duration', 0) for v in all_videos)
        
        stats['total_size_gb'] = round(total_size / (1024**3), 2)
        stats['total_duration_hours'] = round(total_duration / 3600, 2)
        stats['average_size_mb'] = round(total_size / len(all_videos) / (1024**2), 2) if all_videos else 0
        
        return stats
#!/usr/bin/env python3
"""
分析剪映草稿文件
检查为什么只导入了一张图片
"""

import sys
import json
import os
from database import Database

def analyze_draft_content(project_id: int):
    """分析草稿内容"""
    db = Database()
    
    print(f"🔍 分析项目 {project_id} 的草稿内容")
    print("=" * 60)
    
    # 获取项目详情
    project = db.get_project_details(project_id)
    if not project:
        print(f"❌ 项目 {project_id} 不存在")
        return
    
    print(f"📋 项目名称: {project['name']}")
    
    # 检查草稿路径
    draft_path = project.get('jianying_draft_path')
    if not draft_path:
        print("❌ 项目没有草稿路径")
        return
    
    if not os.path.exists(draft_path):
        print(f"❌ 草稿文件夹不存在: {draft_path}")
        return
    
    print(f"📂 草稿路径: {draft_path}")
    
    # 分析draft_content.json
    draft_content_path = os.path.join(draft_path, "draft_content.json")
    if not os.path.exists(draft_content_path):
        print(f"❌ 草稿内容文件不存在: {draft_content_path}")
        return
    
    try:
        with open(draft_content_path, 'r', encoding='utf-8') as f:
            draft_data = json.load(f)
        
        print(f"✅ 草稿文件大小: {os.path.getsize(draft_content_path)} bytes")
        
        # 分析轨道
        tracks = draft_data.get('tracks', [])
        print(f"\n📊 轨道分析:")
        print(f"   总轨道数: {len(tracks)}")
        
        video_tracks = []
        audio_tracks = []
        text_tracks = []
        
        for i, track in enumerate(tracks):
            track_type = track.get('type', 'unknown')
            track_name = track.get('attribute', {}).get('name', f'track_{i}')
            segments = track.get('segments', [])
            
            print(f"   轨道 {i+1}: {track_type} - '{track_name}' ({len(segments)} 个片段)")
            
            if track_type == 'video':
                video_tracks.append(track)
            elif track_type == 'audio':
                audio_tracks.append(track)
            elif track_type == 'text':
                text_tracks.append(track)
        
        # 详细分析视频轨道
        print(f"\n🖼️  视频轨道详细分析:")
        for i, track in enumerate(video_tracks):
            track_name = track.get('attribute', {}).get('name', f'video_track_{i}')
            segments = track.get('segments', [])
            
            print(f"   视频轨道 '{track_name}':")
            print(f"      片段数量: {len(segments)}")
            
            for j, segment in enumerate(segments):
                # 分析片段时间
                target_timerange = segment.get('target_timerange', {})
                start = target_timerange.get('start', 0)
                duration = target_timerange.get('duration', 0)
                end = start + duration
                
                start_sec = start / 1000000
                end_sec = end / 1000000
                duration_sec = duration / 1000000
                
                # 分析素材信息
                material_id = segment.get('material_id', 'unknown')
                clip = segment.get('clip', {})
                
                print(f"      片段 {j+1}:")
                print(f"         时间: {start_sec:.2f}s - {end_sec:.2f}s (持续 {duration_sec:.2f}s)")
                print(f"         素材ID: {material_id}")
                print(f"         片段类型: {segment.get('type', 'unknown')}")
                
                # 检查是否有动画
                animations = segment.get('animations', [])
                if animations:
                    print(f"         动画: {len(animations)} 个")
        
        # 分析素材
        print(f"\n📁 素材分析:")
        materials = draft_data.get('materials', {})
        
        videos = materials.get('videos', [])
        audios = materials.get('audios', [])
        texts = materials.get('texts', [])
        
        print(f"   视频素材: {len(videos)} 个")
        print(f"   音频素材: {len(audios)} 个")
        print(f"   文本素材: {len(texts)} 个")
        
        # 详细分析视频素材
        for i, video_material in enumerate(videos):
            material_id = video_material.get('id', f'video_{i}')
            path = video_material.get('path', 'unknown')
            duration = video_material.get('duration', 0)
            duration_sec = duration / 1000000
            
            print(f"   视频素材 {i+1}:")
            print(f"      ID: {material_id}")
            print(f"      路径: {path}")
            print(f"      时长: {duration_sec:.2f}s")
            print(f"      文件存在: {os.path.exists(path) if path != 'unknown' else 'unknown'}")
        
        # 检查时间重叠问题
        print(f"\n⏰ 时间重叠检查:")
        all_video_segments = []
        for track in video_tracks:
            segments = track.get('segments', [])
            for segment in segments:
                target_timerange = segment.get('target_timerange', {})
                start = target_timerange.get('start', 0)
                duration = target_timerange.get('duration', 0)
                end = start + duration
                
                all_video_segments.append({
                    'start': start,
                    'end': end,
                    'start_sec': start / 1000000,
                    'end_sec': end / 1000000
                })
        
        # 按开始时间排序
        all_video_segments.sort(key=lambda x: x['start'])
        
        overlaps = []
        for i in range(len(all_video_segments) - 1):
            current = all_video_segments[i]
            next_seg = all_video_segments[i + 1]
            
            if current['end'] > next_seg['start']:
                overlaps.append({
                    'segment1': i + 1,
                    'segment2': i + 2,
                    'overlap_start': next_seg['start_sec'],
                    'overlap_end': min(current['end_sec'], next_seg['end_sec'])
                })
        
        if overlaps:
            print(f"   发现 {len(overlaps)} 个时间重叠:")
            for overlap in overlaps:
                print(f"      片段{overlap['segment1']} 和 片段{overlap['segment2']} 重叠: {overlap['overlap_start']:.2f}s - {overlap['overlap_end']:.2f}s")
        else:
            print(f"   ✅ 没有发现时间重叠")
        
        # 对比数据库中的图片数据
        print(f"\n📊 数据库对比:")
        success_images = [img for img in project['images'] if img['status'] == 'success']
        print(f"   数据库中成功图片: {len(success_images)} 张")
        print(f"   草稿中视频片段: {len(all_video_segments)} 个")
        
        if len(success_images) != len(all_video_segments):
            print(f"   ⚠️  数量不匹配！")
            
            print(f"\n   数据库图片时间:")
            for i, img in enumerate(success_images):
                print(f"      图片 {i+1} (场景{img['scene_index']}): {img['start_time']:.2f}s - {img['end_time']:.2f}s")
            
            print(f"\n   草稿片段时间:")
            for i, seg in enumerate(all_video_segments):
                print(f"      片段 {i+1}: {seg['start_sec']:.2f}s - {seg['end_sec']:.2f}s")
        else:
            print(f"   ✅ 数量匹配")
        
    except Exception as e:
        print(f"❌ 分析草稿文件失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    if len(sys.argv) != 2:
        print("用法: python analyze_draft.py <project_id>")
        sys.exit(1)
    
    try:
        project_id = int(sys.argv[1])
        analyze_draft_content(project_id)
    except ValueError:
        print("错误: project_id 必须是数字")
        sys.exit(1)

if __name__ == "__main__":
    main()

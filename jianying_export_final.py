import os
import json
from typing import List, Dict, Optional
from datetime import datetime
from database import Database
from file_manager import FileManager
import pyJianYingDraft as draft
from pyJianYingDraft import (
    Script_file, Draft_folder,
    Video_segment, Audio_segment, Text_segment,
    Video_material, Audio_material,
    Track_type, Font_type,
    Intro_type, Outro_type, Group_animation_type,
    Text_style, Clip_settings,
    trange, SEC, Timerange
)

class FinalJianyingExporter:
    """
    最终版剪映导出器
    解决所有已知问题，确保图片正常显示
    """
    
    def __init__(self, jianying_drafts_path: str):
        self.drafts_path = jianying_drafts_path
        self.draft_folder = Draft_folder(jianying_drafts_path)
        self.db = Database()
        self.file_manager = FileManager()
    
    def create_draft_from_project(self, project_id: int, draft_name: Optional[str] = None) -> bool:
        """
        从项目创建剪映草稿（最终版）
        """
        try:
            print(f"🚀 开始创建最终版剪映草稿...")
            
            # 1. 修复数据存储格式
            if not self._fix_project_data(project_id):
                return False
            
            # 2. 创建草稿
            return self._create_optimized_draft(project_id, draft_name)
            
        except Exception as e:
            print(f"❌ 创建剪映草稿时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    def _fix_project_data(self, project_id: int) -> bool:
        """修复项目数据"""
        print(f"🔧 修复项目数据...")
        
        # 获取项目详情
        project = self.db.get_project_details(project_id)
        if not project:
            print(f"❌ 项目 {project_id} 不存在")
            return False
        
        # 获取真实音频时长
        if not project['audio_files']:
            print("❌ 没有音频文件")
            return False
        
        audio_path = project['audio_files'][0]['file_path']
        if not os.path.exists(audio_path):
            print(f"❌ 音频文件不存在: {audio_path}")
            return False
        
        try:
            audio_material = draft.Audio_material(audio_path)
            real_audio_duration_sec = audio_material.duration / 1000000
            print(f"✅ 真实音频时长: {real_audio_duration_sec:.2f}s")
        except Exception as e:
            print(f"❌ 获取音频时长失败: {str(e)}")
            return False
        
        # 检查图片
        success_images = [img for img in project['images'] if img['status'] == 'success']
        if not success_images:
            print("❌ 没有成功的图片")
            return False
        
        # 验证图片文件
        valid_images = []
        for img in success_images:
            if os.path.exists(img['file_path']):
                valid_images.append(img)
        
        if not valid_images:
            print("❌ 没有有效的图片文件")
            return False
        
        print(f"✅ 有效图片: {len(valid_images)} 张")
        
        # 重新分配时间
        valid_images.sort(key=lambda x: x['scene_index'])
        image_duration_sec = real_audio_duration_sec / len(valid_images)
        
        print(f"⏰ 重新分配时间: 每张图片 {image_duration_sec:.2f}s")
        
        for i, img in enumerate(valid_images):
            start_time_sec = i * image_duration_sec
            end_time_sec = (i + 1) * image_duration_sec
            
            if end_time_sec > real_audio_duration_sec:
                end_time_sec = real_audio_duration_sec
            
            try:
                self.db.conn.execute(
                    "UPDATE images SET start_time = ?, end_time = ? WHERE id = ?",
                    (start_time_sec, end_time_sec, img['id'])
                )
            except Exception as e:
                print(f"❌ 更新图片时间失败: {str(e)}")
                return False
        
        # 提交更改
        try:
            self.db.conn.commit()
            print(f"✅ 数据修复完成")
            return True
        except Exception as e:
            print(f"❌ 提交更改失败: {str(e)}")
            self.db.conn.rollback()
            return False
    
    def _create_optimized_draft(self, project_id: int, draft_name: Optional[str] = None) -> bool:
        """创建优化的草稿"""
        print(f"🎬 创建优化草稿...")
        
        # 获取修复后的项目数据
        project = self.db.get_project_details(project_id)
        if not project:
            return False
        
        if not draft_name:
            draft_name = f"{project['name']}_final_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        print(f"📝 草稿名称: {draft_name}")
        
        # 创建Script对象
        script = draft.Script_file(1920, 1080)
        
        # 1. 添加音频轨道
        if not self._add_audio_track(script, project):
            return False
        
        # 2. 添加视频轨道（图片）
        if not self._add_video_track(script, project):
            return False
        
        # 3. 添加字幕轨道
        if not self._add_subtitle_track(script, project):
            return False
        
        # 4. 保存草稿
        return self._save_draft(script, draft_name, project_id)
    
    def _add_audio_track(self, script: draft.Script_file, project: Dict) -> bool:
        """添加音频轨道"""
        print(f"🎵 添加音频轨道...")
        
        try:
            script.add_track(draft.Track_type.audio, "主音频")
            
            audio_info = project['audio_files'][0]
            audio_path = audio_info['file_path']
            
            audio_material = draft.Audio_material(audio_path)
            audio_segment = draft.Audio_segment(audio_material, draft.trange(0, audio_material.duration))
            script.add_segment(audio_segment, "主音频")
            
            print(f"✅ 音频轨道添加成功")
            return True
            
        except Exception as e:
            print(f"❌ 添加音频轨道失败: {str(e)}")
            return False
    
    def _add_video_track(self, script: draft.Script_file, project: Dict) -> bool:
        """添加视频轨道"""
        print(f"🖼️  添加视频轨道...")
        
        try:
            script.add_track(draft.Track_type.video, "分镜图片")
            
            success_images = [img for img in project['images'] if img['status'] == 'success']
            success_images.sort(key=lambda x: x['scene_index'])
            
            added_count = 0
            for i, image_info in enumerate(success_images):
                if self._add_single_image(script, image_info, i, len(success_images)):
                    added_count += 1
            
            print(f"✅ 成功添加 {added_count}/{len(success_images)} 张图片")
            return added_count > 0
            
        except Exception as e:
            print(f"❌ 添加视频轨道失败: {str(e)}")
            return False
    
    def _add_single_image(self, script: draft.Script_file, image_info: Dict, index: int, total_images: int) -> bool:
        """添加单张图片"""
        image_path = image_info['file_path']
        start_time_sec = image_info['start_time']
        end_time_sec = image_info['end_time']

        print(f"   🎨 处理图片 {index+1}: 场景{image_info['scene_index']}")
        print(f"      时间: {start_time_sec:.2f}s - {end_time_sec:.2f}s")

        if not os.path.exists(image_path):
            print(f"      ❌ 文件不存在")
            return False

        try:
            start_time_us = int(start_time_sec * 1000000)
            end_time_us = int(end_time_sec * 1000000)
            duration_us = end_time_us - start_time_us

            if duration_us <= 0:
                print(f"      ❌ 持续时间无效")
                return False

            # 创建图片素材和片段
            image_material = draft.Video_material(image_path)
            image_segment = draft.Video_segment(
                image_material,
                draft.trange(start_time_us, duration_us),
                clip_settings=draft.Clip_settings(
                    scale_x=1.0,
                    scale_y=1.0,
                    transform_x=0.0,
                    transform_y=0.0
                )
            )

            # 添加动画效果
            self._add_animations_to_image(image_segment, index, total_images)

            # 添加到轨道
            script.add_segment(image_segment, "分镜图片")
            print(f"      ✅ 添加成功")
            return True

        except Exception as e:
            print(f"      ❌ 添加失败: {str(e)}")
            return False

    def _add_animations_to_image(self, image_segment: draft.Video_segment, index: int, total_images: int):
        """为图片添加动画效果"""
        try:
            # 获取实际可用的动画列表
            available_intros = [attr for attr in dir(draft.Intro_type) if not attr.startswith('_')]
            available_outros = [attr for attr in dir(draft.Outro_type) if not attr.startswith('_')]
            available_groups = [attr for attr in dir(draft.Group_animation_type) if not attr.startswith('_')]

            print(f"      🎭 可用动画: 入场{len(available_intros)}个, 出场{len(available_outros)}个, 组合{len(available_groups)}个")

            # 策略1: 第一张图片 - 入场动画
            if index == 0 and available_intros:
                # 选择一个好看的入场动画
                preferred_intros = ["渐显", "放大", "向右滑动", "展开", "弹近"]
                intro_anim = None

                for pref in preferred_intros:
                    if pref in available_intros:
                        intro_anim = pref
                        break

                if not intro_anim:
                    intro_anim = available_intros[0]  # 使用第一个可用的

                image_segment.add_animation(getattr(draft.Intro_type, intro_anim))
                print(f"      ✨ 添加入场动画: {intro_anim}")

            # 策略2: 最后一张图片 - 出场动画
            elif index == total_images - 1 and available_outros:
                # 选择一个好看的出场动画
                preferred_outros = ["渐隐", "缩小", "向左滑动", "折叠", "弹远"]
                outro_anim = None

                for pref in preferred_outros:
                    if pref in available_outros:
                        outro_anim = pref
                        break

                if not outro_anim:
                    outro_anim = available_outros[0]  # 使用第一个可用的

                image_segment.add_animation(getattr(draft.Outro_type, outro_anim))
                print(f"      ✨ 添加出场动画: {outro_anim}")

            # 策略3: 中间图片 - 组合动画
            elif available_groups:
                # 为中间的图片添加组合动画
                group_anim = available_groups[index % len(available_groups)]
                image_segment.add_animation(getattr(draft.Group_animation_type, group_anim))
                print(f"      ✨ 添加组合动画: {group_anim}")

            # 策略4: 备选方案 - 如果没有组合动画，使用入场动画
            elif available_intros:
                intro_anim = available_intros[index % len(available_intros)]
                image_segment.add_animation(getattr(draft.Intro_type, intro_anim))
                print(f"      ✨ 添加入场动画: {intro_anim}")

            else:
                print(f"      ⚠️  没有可用的动画类型")

        except Exception as e:
            print(f"      ⚠️  添加动画失败: {str(e)}")
            # 动画添加失败不影响图片本身的添加
    
    def _add_subtitle_track(self, script: draft.Script_file, project: Dict) -> bool:
        """添加字幕轨道"""
        print(f"📝 添加字幕轨道...")
        
        try:
            script.add_track(draft.Track_type.text, "字幕")
            
            subtitle_info = project['subtitles'][0]
            srt_path = subtitle_info['srt_file_path']
            
            script.import_srt(
                srt_path,
                track_name="字幕",
                text_style=draft.Text_style(
                    size=6.0,
                    color=(1.0, 1.0, 1.0),
                    bold=True,
                    align=1
                ),
                clip_settings=draft.Clip_settings(
                    transform_y=-0.8
                )
            )
            
            print(f"✅ 字幕轨道添加成功")
            return True
            
        except Exception as e:
            print(f"❌ 添加字幕轨道失败: {str(e)}")
            return False
    
    def _save_draft(self, script: draft.Script_file, draft_name: str, project_id: int) -> bool:
        """保存草稿"""
        print(f"💾 保存草稿...")
        
        try:
            draft_dir = os.path.join(self.drafts_path, draft_name)
            os.makedirs(draft_dir, exist_ok=True)
            
            draft_content_path = os.path.join(draft_dir, "draft_content.json")
            script.dump(draft_content_path)
            
            if not os.path.exists(draft_content_path):
                print("❌ 草稿文件生成失败")
                return False
            
            file_size = os.path.getsize(draft_content_path)
            print(f"📄 草稿文件: {file_size} bytes")
            
            # 创建meta信息
            meta_info = {
                "draft_id": str(os.urandom(16).hex()),
                "draft_name": draft_name,
                "draft_folder": draft_name,
                "create_time": int(datetime.now().timestamp() * 1000),
                "update_time": int(datetime.now().timestamp() * 1000),
                "duration": 0,
                "cover_path": "",
                "fps": 30,
                "resolution": {"width": 1920, "height": 1080}
            }
            
            meta_info_path = os.path.join(draft_dir, "draft_meta_info.json")
            with open(meta_info_path, 'w', encoding='utf-8') as f:
                json.dump(meta_info, f, ensure_ascii=False, indent=2)
            
            # 更新数据库
            self.db.conn.execute(
                "UPDATE projects SET jianying_draft_path = ? WHERE id = ?",
                (draft_dir, project_id)
            )
            self.db.conn.commit()
            
            print(f"🎉 最终版剪映草稿创建成功！")
            print(f"📂 草稿位置: {draft_dir}")
            print("💡 此版本已解决所有已知问题，图片应该正常显示")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存草稿失败: {str(e)}")
            return False

#!/usr/bin/env python3
"""
测试火山引擎ASR字幕精确度的脚本
用于验证和调试字幕时间戳的准确性
"""

import os
import sys
import time
import tempfile
from dotenv import load_dotenv

# 导入火山引擎API
try:
    from volcano_api import VolcanoSpeechAPI, VolcanoTTSAPI, convert_to_srt
except ImportError:
    print("错误：无法导入volcano_api模块")
    sys.exit(1)

load_dotenv()

def test_subtitle_accuracy():
    """测试字幕准确性"""
    # 获取配置
    app_id = os.getenv('VOLCANO_APP_ID')
    access_token = os.getenv('VOLCANO_ACCESS_TOKEN')
    
    if not app_id or not access_token:
        print("错误：请配置VOLCANO_APP_ID和VOLCANO_ACCESS_TOKEN")
        return
    
    # 初始化API
    speech_api = VolcanoSpeechAPI(app_id, access_token)
    tts_api = VolcanoTTSAPI(app_id, access_token)
    
    # 测试文本
    test_texts = [
        "这是第一句测试文本。",
        "火山引擎的语音识别技术非常先进，能够准确识别各种语音内容。",
        "我们正在测试字幕和音频的同步效果，希望能够达到严丝合缝的效果。",
        "通过不断的测试和优化，我们可以获得最佳的字幕显示效果。"
    ]
    
    for idx, text in enumerate(test_texts, 1):
        print(f"\n{'='*60}")
        print(f"测试 {idx}: {text}")
        print('='*60)
        
        # 1. 生成TTS音频
        print("\n1. 生成TTS音频...")
        submit_result = tts_api.submit_task(text, "BV001_streaming")
        if submit_result.get("code") != 0:
            print(f"TTS提交失败: {submit_result.get('message')}")
            continue
            
        task_id = submit_result.get("task_id")
        print(f"TTS任务ID: {task_id}")
        
        # 等待TTS结果
        time.sleep(2)
        result = tts_api.query_result(task_id)
        
        if result.get("code") != 0:
            print(f"TTS失败: {result.get('message')}")
            continue
            
        # 下载音频
        audio_url = result.get("audio_url")
        if not audio_url:
            print("未获取到音频URL")
            continue
            
        # 保存音频
        temp_audio = tempfile.NamedTemporaryFile(suffix='.mp3', delete=False)
        temp_audio_path = temp_audio.name
        temp_audio.close()
        
        import requests
        response = requests.get(audio_url)
        if response.status_code != 200:
            print(f"下载音频失败: {response.status_code}")
            continue
            
        with open(temp_audio_path, 'wb') as f:
            f.write(response.content)
        print(f"音频已保存: {temp_audio_path}")
        
        # 获取TTS返回的时间信息（如果有）
        tts_sentences = result.get("sentences", [])
        if tts_sentences:
            print("\nTTS返回的时间信息：")
            for sent in tts_sentences:
                begin = sent.get("begin_time", 0) / 1000
                end = sent.get("end_time", 0) / 1000
                text_part = sent.get("text", "")
                print(f"  [{begin:.3f}s - {end:.3f}s]: {text_part}")
        
        # 2. 使用ASR获取精确字幕
        print("\n2. 使用ASR分析音频...")
        with open(temp_audio_path, 'rb') as audio_file:
            submit_result = speech_api.submit_audio(
                audio_file=audio_file,
                words_per_line=20,
                max_lines=2,
                language="zh-CN",
                use_punc=True,
                caption_type="speech"
            )
        
        if submit_result.get("code") != 0:
            print(f"ASR提交失败: {submit_result.get('message')}")
            os.unlink(temp_audio_path)
            continue
            
        asr_task_id = submit_result.get("id")
        print(f"ASR任务ID: {asr_task_id}")
        
        # 等待ASR结果
        print("等待ASR处理...")
        asr_result = speech_api.query_result(asr_task_id, blocking=True)
        
        if asr_result.get("code") != 0:
            print(f"ASR失败: {asr_result.get('message')}")
            os.unlink(temp_audio_path)
            continue
            
        # 获取字幕
        utterances = asr_result.get("utterances", [])
        print(f"\nASR返回 {len(utterances)} 条字幕：")
        
        for utt in utterances:
            start = utt['start_time'] / 1000
            end = utt['end_time'] / 1000
            duration = end - start
            text = utt['text']
            print(f"  [{start:.3f}s - {end:.3f}s] (时长: {duration:.3f}s): {text}")
        
        # 3. 对比分析
        print("\n3. 时间戳对比分析：")
        if tts_sentences and utterances:
            print("TTS时间 vs ASR时间：")
            # 这里可以添加更详细的对比逻辑
        
        # 保存SRT文件以便查看
        srt_content = convert_to_srt(utterances)
        srt_path = f"test_subtitle_{idx}.srt"
        with open(srt_path, 'w', encoding='utf-8') as f:
            f.write(srt_content)
        print(f"\n字幕已保存: {srt_path}")
        
        # 清理临时文件
        os.unlink(temp_audio_path)
        
        # 等待一下避免API限流
        time.sleep(1)
    
    print("\n测试完成！")
    print("你可以使用视频播放器打开音频文件和对应的SRT字幕文件来验证同步效果。")

def test_single_audio_file(audio_path: str):
    """测试单个音频文件的字幕生成"""
    # 获取配置
    app_id = os.getenv('VOLCANO_APP_ID')
    access_token = os.getenv('VOLCANO_ACCESS_TOKEN')
    
    if not app_id or not access_token:
        print("错误：请配置VOLCANO_APP_ID和VOLCANO_ACCESS_TOKEN")
        return
    
    # 初始化API
    speech_api = VolcanoSpeechAPI(app_id, access_token)
    
    print(f"分析音频文件: {audio_path}")
    
    # 提交ASR任务
    with open(audio_path, 'rb') as audio_file:
        submit_result = speech_api.submit_audio(
            audio_file=audio_file,
            words_per_line=15,  # 减少每行字数
            max_lines=2,
            language="zh-CN",
            use_punc=True,
            caption_type="speech"
        )
    
    if submit_result.get("code") != 0:
        print(f"ASR提交失败: {submit_result.get('message')}")
        return
        
    task_id = submit_result.get("id")
    print(f"ASR任务ID: {task_id}")
    
    # 查询结果
    print("等待ASR处理...")
    result = speech_api.query_result(task_id, blocking=True)
    
    if result.get("code") != 0:
        print(f"ASR失败: {result.get('message')}")
        return
        
    # 获取字幕
    utterances = result.get("utterances", [])
    print(f"\n获取到 {len(utterances)} 条字幕：")
    
    # 详细显示每条字幕
    total_duration = 0
    for i, utt in enumerate(utterances, 1):
        start = utt['start_time'] / 1000
        end = utt['end_time'] / 1000
        duration = end - start
        text = utt['text']
        words = len(text)
        
        print(f"\n字幕 {i}:")
        print(f"  时间: {start:.3f}s - {end:.3f}s")
        print(f"  时长: {duration:.3f}s")
        print(f"  文本: {text}")
        print(f"  字数: {words} (速度: {words/duration:.1f}字/秒)")
        
        total_duration = max(total_duration, end)
    
    print(f"\n总时长: {total_duration:.3f}s")
    
    # 保存SRT文件
    srt_content = convert_to_srt(utterances)
    srt_path = audio_path.replace('.mp3', '_asr.srt').replace('.wav', '_asr.srt')
    with open(srt_path, 'w', encoding='utf-8') as f:
        f.write(srt_content)
    print(f"\n字幕已保存: {srt_path}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 如果提供了音频文件路径，测试单个文件
        test_single_audio_file(sys.argv[1])
    else:
        # 否则运行完整测试
        test_subtitle_accuracy()